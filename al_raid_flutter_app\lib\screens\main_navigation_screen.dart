import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/cart_provider.dart';
import '../providers/language_provider.dart';

import '../screens/home_screen.dart';
import '../screens/cart_screen.dart';
import '../screens/profile_screen.dart';
import '../screens/add_product_screen.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isMerchant = authProvider.user?.isMerchant ?? false;

    // Different screens based on user type
    final buyerScreens = [
      const HomeScreen(),
      const OrdersScreen(), // تغيير السلة إلى الطلبات
      const ProfileScreen(),
    ];

    final merchantScreens = [
      const HomeScreen(),
      const AddProductScreen(),
      const OrdersScreen(),
      const ProfileScreen(),
    ];

    final screens = isMerchant ? merchantScreens : buyerScreens;

    return Scaffold(
      body: IndexedStack(index: _currentIndex, children: screens),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              theme.colorScheme.surface.withOpacity(0.8),
              theme.colorScheme.surface,
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          child: _buildBottomNavigationBar(theme, isMerchant),
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar(ThemeData theme, bool isMerchant) {
    return Consumer2<CartProvider, LanguageProvider>(
      builder: (context, cartProvider, languageProvider, child) {
        final cartItemCount = cartProvider.itemCount;
        final isRTL = languageProvider.isArabic;

        return BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedItemColor: theme.colorScheme.primary,
          unselectedItemColor:
              theme.colorScheme.onSurface.withValues(alpha: 0.6),
          selectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 11,
          ),
          items: isMerchant
              ? _merchantNavItems(theme)
              : _buyerNavItems(theme, cartItemCount, isRTL),
        );
      },
    );
  }

  List<BottomNavigationBarItem> _buyerNavItems(
    ThemeData theme,
    int cartItemCount,
    bool isRTL,
  ) {
    return [
      BottomNavigationBarItem(
        icon: _buildNavIcon(Icons.home_outlined, 0),
        activeIcon: _buildActiveNavIcon(Icons.home, 0),
        label: isRTL ? 'الرئيسية' : 'Home',
      ),
      BottomNavigationBarItem(
        icon: _buildNavIcon(Icons.receipt_long_outlined, 1),
        activeIcon: _buildActiveNavIcon(Icons.receipt_long, 1),
        label: isRTL ? 'طلباتي' : 'Orders',
      ),
      BottomNavigationBarItem(
        icon: _buildNavIcon(Icons.person_outline, 2),
        activeIcon: _buildActiveNavIcon(Icons.person, 2),
        label: isRTL ? 'الملف الشخصي' : 'Profile',
      ),
    ];
  }

  List<BottomNavigationBarItem> _merchantNavItems(ThemeData theme) {
    return [
      BottomNavigationBarItem(
        icon: _buildNavIcon(Icons.home_outlined, 0),
        activeIcon: _buildActiveNavIcon(Icons.home, 0),
        label: 'Home',
      ),
      BottomNavigationBarItem(
        icon: _buildNavIcon(Icons.add_business_outlined, 1),
        activeIcon: _buildActiveNavIcon(Icons.add_business, 1),
        label: 'Add Product',
      ),
      BottomNavigationBarItem(
        icon: _buildNavIcon(Icons.assignment_outlined, 2),
        activeIcon: _buildActiveNavIcon(Icons.assignment, 2),
        label: 'Orders',
      ),
      BottomNavigationBarItem(
        icon: _buildNavIcon(Icons.person_outline, 3),
        activeIcon: _buildActiveNavIcon(Icons.person, 3),
        label: 'Profile',
      ),
    ];
  }

  Widget _buildNavIcon(IconData icon, int index) {
    final isSelected = _currentIndex == index;
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isSelected
            ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
            : Colors.transparent,
      ),
      child: Icon(icon, size: 24),
    );
  }

  Widget _buildActiveNavIcon(IconData icon, int index) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.secondary,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Icon(icon, size: 24, color: Colors.white),
    ).animate().scale(duration: 200.ms);
  }

  Widget _buildCartIcon(
    IconData icon,
    int index,
    int itemCount, {
    bool isActive = false,
  }) {
    final theme = Theme.of(context);
    final isSelected = _currentIndex == index;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: isActive
                ? LinearGradient(
                    colors: [
                      theme.colorScheme.primary,
                      theme.colorScheme.secondary,
                    ],
                  )
                : null,
            color: isSelected && !isActive
                ? theme.colorScheme.primary.withOpacity(0.1)
                : Colors.transparent,
            boxShadow: isActive
                ? [
                    BoxShadow(
                      color: theme.colorScheme.primary.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Icon(icon, size: 24, color: isActive ? Colors.white : null),
        ),
        if (itemCount > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.red.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              constraints: const BoxConstraints(minWidth: 20, minHeight: 20),
              child: Text(
                itemCount > 99 ? '99+' : itemCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ).animate().scale(duration: 300.ms).shake(duration: 200.ms),
          ),
      ],
    );
  }
}
