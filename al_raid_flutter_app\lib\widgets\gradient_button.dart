import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class GradientButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool enabled;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final Gradient? gradient;
  final TextStyle? textStyle;
  final Widget? icon;
  final bool isOutlined;

  const GradientButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.enabled = true,
    this.width,
    this.height = 56,
    this.padding,
    this.margin,
    this.borderRadius,
    this.gradient,
    this.textStyle,
    this.icon,
    this.isOutlined = false,
  }) : super(key: key);

  @override
  State<GradientButton> createState() => _GradientButtonState();
}

class _GradientButtonState extends State<GradientButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  void _handleTapDown(TapDownDetails details) {
    if (widget.enabled && !widget.isLoading) {
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.enabled && !widget.isLoading) {
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (widget.enabled && !widget.isLoading) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    final defaultGradient = widget.gradient ?? LinearGradient(
      colors: widget.isOutlined 
          ? [Colors.transparent, Colors.transparent]
          : [
              theme.primaryColor,
              theme.primaryColor.withValues(alpha: 0.8),
            ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );

    final defaultTextStyle = widget.textStyle ?? TextStyle(
      color: widget.isOutlined 
          ? theme.primaryColor 
          : Colors.white,
      fontSize: 16,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.5,
    );

    return Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: GestureDetector(
              onTapDown: _handleTapDown,
              onTapUp: _handleTapUp,
              onTapCancel: _handleTapCancel,
              onTap: widget.enabled && !widget.isLoading ? widget.onPressed : null,
              child: Container(
                decoration: BoxDecoration(
                  gradient: widget.enabled ? defaultGradient : LinearGradient(
                    colors: [Colors.grey.shade400, Colors.grey.shade300],
                  ),
                  borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
                  border: widget.isOutlined ? Border.all(
                    color: widget.enabled ? theme.primaryColor : Colors.grey.shade400,
                    width: 2,
                  ) : null,
                  boxShadow: widget.enabled && !widget.isOutlined ? [
                    BoxShadow(
                      color: theme.primaryColor.withValues(alpha: 0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 8),
                    ),
                    BoxShadow(
                      color: isDark ? Colors.black.withValues(alpha: 0.3) : Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ] : null,
                ),
                child: Material(
                  color: Colors.transparent,
                  child: Container(
                    padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                    child: widget.isLoading
                        ? Center(
                            child: SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  widget.isOutlined ? theme.primaryColor : Colors.white,
                                ),
                              ),
                            ),
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (widget.icon != null) ...[
                                widget.icon!,
                                const SizedBox(width: 8),
                              ],
                              Text(
                                widget.text,
                                style: defaultTextStyle,
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

class FloatingActionGradientButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget icon;
  final String? heroTag;
  final Gradient? gradient;
  final bool mini;

  const FloatingActionGradientButton({
    Key? key,
    required this.onPressed,
    required this.icon,
    this.heroTag,
    this.gradient,
    this.mini = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    final defaultGradient = gradient ?? LinearGradient(
      colors: [
        theme.primaryColor,
        theme.primaryColor.withValues(alpha: 0.8),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );

    return FloatingActionButton(
      onPressed: onPressed,
      heroTag: heroTag,
      mini: mini,
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Container(
        width: mini ? 40 : 56,
        height: mini ? 40 : 56,
        decoration: BoxDecoration(
          gradient: defaultGradient,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: theme.primaryColor.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: icon,
      ),
    ).animate()
      .fadeIn(duration: 600.ms)
      .scale(
        begin: const Offset(0.8, 0.8),
        end: const Offset(1.0, 1.0),
        duration: 400.ms,
        curve: Curves.elasticOut,
      );
  }
}
