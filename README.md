# AL-Raid Export Platform MVP

A comprehensive digital platform for global trade, connecting merchants and buyers worldwide. The platform consists of a Flutter mobile application and a PHP-based web backend with admin dashboard.

## 🚀 Features

### Mobile Application (Flutter)
- **User Registration**: Separate registration for merchants and buyers
- **Product Management**: Merchants can add and manage their products
- **Product Browsing**: Browse products by categories (Industrial/Food)
- **Search Functionality**: Search products by description and origin
- **Order Management**: Buyers can request products
- **Multilingual Support**: Arabic and English localization
- **User Profiles**: Comprehensive user profile management

### Backend & Admin Dashboard (PHP)
- **RESTful API**: Complete API for mobile app integration
- **Admin Dashboard**: Web-based admin panel for management
- **User Management**: View and manage registered users
- **Product Review**: Approve/reject product submissions
- **Order Management**: Review and approve order requests
- **File Upload**: Handle product images and commercial register documents

## 📱 Mobile App Screenshots

The Flutter app includes:
- Splash screen with terms and conditions
- User registration with image upload
- Home screen with featured products
- Product listing by categories
- Product detail pages
- Search functionality
- User profile management

## 🛠️ Technology Stack

### Frontend (Mobile)
- **Flutter**: Cross-platform mobile development
- **Provider**: State management
- **Dio**: HTTP client for API communication
- **Image Picker**: Camera and gallery integration
- **Cached Network Image**: Efficient image loading
- **Flutter Localizations**: Internationalization support

### Backend
- **PHP**: Server-side logic
- **MySQL**: Database management
- **PDO**: Database abstraction layer
- **File Upload**: Image and document handling

## 📋 Prerequisites

### For Flutter App
- Flutter SDK (latest stable version)
- Android Studio / VS Code
- Android SDK / iOS development tools

### For PHP Backend
- XAMPP/WAMP/LAMP server
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)

## 🚀 Installation & Setup

### 1. Backend Setup

1. **Start your local server** (XAMPP/WAMP)

2. **Clone/Copy the project** to your web server directory:
   ```
   C:\xampp\htdocs\ALraid (for XAMPP on Windows)
   ```

3. **Setup the database**:
   - Open your browser and navigate to: `http://localhost/ALraid/al_raid_backend/setup_database.php`
   - This will create the database, tables, and sample data

4. **Verify backend**:
   - Admin Dashboard: `http://localhost/ALraid/al_raid_backend/admin/`
   - API Endpoint: `http://localhost/ALraid/al_raid_backend/api/`

### 2. Flutter App Setup

1. **Navigate to the Flutter app directory**:
   ```bash
   cd al_raid_app
   ```

2. **Install dependencies**:
   ```bash
   flutter pub get
   ```

3. **Generate localization files**:
   ```bash
   flutter gen-l10n
   ```

4. **Update API base URL** (if needed):
   - Edit `lib/services/api_service.dart`
   - Update the `baseUrl` to match your server configuration

5. **Run the app**:
   ```bash
   flutter run
   ```

## 🔧 Configuration

### Database Configuration
Edit `al_raid_backend/config/database.php` to match your database settings:
```php
private $host = 'localhost';
private $db_name = 'al_raid_db';
private $username = 'root';
private $password = '';
```

### API Configuration
Update the API base URL in `al_raid_app/lib/services/api_service.dart`:
```dart
static const String baseUrl = 'http://your-server/al_raid_backend/api';
```

## 👤 Default Admin Credentials

- **Username**: admin
- **Password**: admin123

## 📊 Database Schema

### Users Table
- User information (merchants and buyers)
- Commercial register document storage
- Role-based access control

### Products Table
- Product details and images
- Category classification (industrial/food)
- Status management (under review/approved/rejected)

### Orders Table
- Order requests from buyers
- Status tracking and approval workflow

### Admins Table
- Admin user management
- Secure password storage

## 🔗 API Endpoints

### User Management
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `GET /api/user/{id}` - Get user profile

### Product Management
- `GET /api/products` - Get all approved products
- `GET /api/products?type={type}` - Get products by type
- `POST /api/products` - Add new product (merchants only)

### Order Management
- `POST /api/orders` - Create new order
- `GET /api/orders?user_id={id}` - Get user orders

## 📱 Mobile App Features

### Authentication Flow
1. Splash screen with app introduction
2. User registration with role selection
3. Commercial register upload for merchants
4. Automatic login after registration

### Product Management
1. Browse products by category
2. Search functionality
3. Product detail views
4. Add products (merchants)
5. Request products (buyers)

### User Experience
1. Multilingual support (Arabic/English)
2. Responsive design
3. Image caching and optimization
4. Error handling and loading states

## 🔒 Security Features

- Input validation and sanitization
- File upload restrictions
- SQL injection prevention using PDO
- Role-based access control
- Admin authentication

## 📈 Future Enhancements

- Push notifications
- Real-time chat between merchants and buyers
- Payment integration
- Advanced search filters
- Product reviews and ratings
- Analytics dashboard
- Email notifications
- Multi-currency support

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**:
   - Verify MySQL server is running
   - Check database credentials in config file

2. **API Not Working**:
   - Ensure web server is running
   - Check file permissions
   - Verify API endpoint URLs

3. **Flutter Build Issues**:
   - Run `flutter clean` and `flutter pub get`
   - Check Flutter and Dart SDK versions

4. **Image Upload Issues**:
   - Verify upload directory permissions
   - Check file size limits in PHP configuration

## 📞 Support

For technical support or questions about the AL-Raid Export Platform, please refer to the documentation or contact the development team.

## 📄 License

This project is developed as an MVP for AL-Raid Export Platform. All rights reserved.

---

**AL-Raid Export Platform** - Connecting Global Trade 🌍
