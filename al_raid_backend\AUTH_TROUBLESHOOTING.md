# دليل حل مشاكل التسجيل وتسجيل الدخول - AL Raid

## المشكلة الأساسية المحلولة
```
SQLSTATE[42S22]: Column not found: 1054 Unknown column 'password' in 'INSERT INTO'
```

## التحسينات المطبقة

### 1. إصلاح بنية قاعدة البيانات
- ✅ إضافة عمود `password` إلى جدول `users`
- ✅ تحديث جميع ملفات إنشاء قاعدة البيانات
- ✅ إنشاء ملف `update_database.php` لتحديث قاعدة البيانات الموجودة

### 2. تحسين التحقق من صحة البيانات
- ✅ التحقق من صيغة البريد الإلكتروني
- ✅ التحقق من قوة كلمة المرور (6 أحرف على الأقل)
- ✅ التحقق من صيغة رقم الهاتف
- ✅ التحقق من صحة الدور (merchant/buyer)
- ✅ رسائل خطأ أكثر وضوحاً

### 3. تحسين أمان تسجيل الدخول
- ✅ التحقق من وجود المستخدم قبل التحقق من كلمة المرور
- ✅ رسائل خطأ موحدة لمنع تسريب معلومات المستخدمين
- ✅ إزالة كلمة المرور من الاستجابة

## خطوات الإصلاح

### 1. تحديث قاعدة البيانات
افتح: `http://localhost/ALraid/al_raid_backend/update_database.php`

هذا الملف سيقوم بـ:
- إضافة عمود `password` إذا لم يكن موجوداً
- تعيين كلمة مرور افتراضية للمستخدمين الموجودين
- عرض بنية الجدول الحالية

### 2. اختبار التسجيل وتسجيل الدخول
افتح: `http://localhost/ALraid/al_raid_backend/test_auth.html`

يمكنك:
- تسجيل مستخدمين جدد
- تسجيل الدخول بالمستخدمين الموجودين
- اختبار التحقق من صحة البيانات

### 3. المستخدمون التجريبيون
كلمة المرور لجميع المستخدمين التجريبيين: `123456`

- **<EMAIL>** - تاجر من السعودية
- **<EMAIL>** - مشتري من الإمارات
- **<EMAIL>** - تاجر من قطر
- **<EMAIL>** - مشتري من الكويت

## بنية قاعدة البيانات المحدثة

### جدول users
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone_number VARCHAR(50) NOT NULL,
    password VARCHAR(255) NOT NULL,
    commercial_register_photo VARCHAR(255),
    shipping_address TEXT NOT NULL,
    country VARCHAR(100) NOT NULL,
    role ENUM('merchant', 'buyer') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## قواعد التحقق من صحة البيانات

### التسجيل
- **الاسم الكامل**: مطلوب
- **البريد الإلكتروني**: مطلوب، صيغة صحيحة، غير مكرر
- **رقم الهاتف**: مطلوب، صيغة صحيحة
- **كلمة المرور**: مطلوب، 6 أحرف على الأقل
- **العنوان**: مطلوب
- **الدولة**: مطلوب
- **الدور**: مطلوب (merchant أو buyer)

### تسجيل الدخول
- **البريد الإلكتروني**: مطلوب، صيغة صحيحة
- **كلمة المرور**: مطلوب

## اختبار التطبيق

### 1. تسجيل مستخدم جديد
```dart
// في التطبيق
final success = await authProvider.register(
  fullName: 'اسم المستخدم',
  email: '<EMAIL>',
  phoneNumber: '+966501234567',
  shippingAddress: 'العنوان',
  country: 'Saudi Arabia',
  role: UserRole.buyer,
  password: '123456',
);
```

### 2. تسجيل الدخول
```dart
// في التطبيق
final success = await authProvider.login(
  '<EMAIL>',
  '123456',
);
```

## حل المشاكل الشائعة

### مشكلة: "Column 'password' not found"
**الحل:** تشغيل `update_database.php` لإضافة العمود المفقود

### مشكلة: "Invalid email format"
**الحل:** التأكد من صيغة البريد الإلكتروني الصحيحة

### مشكلة: "Password must be at least 6 characters"
**الحل:** استخدام كلمة مرور أطول من 6 أحرف

### مشكلة: "Email already exists"
**الحل:** استخدام بريد إلكتروني مختلف أو تسجيل الدخول بالحساب الموجود

### مشكلة: "Invalid email or password"
**الحل:** التأكد من صحة البيانات أو إعادة تعيين كلمة المرور

## ملفات الاختبار المتاحة

1. `update_database.php` - تحديث قاعدة البيانات
2. `test_auth.html` - اختبار التسجيل وتسجيل الدخول
3. `setup_database.php` - إنشاء قاعدة البيانات من الصفر

## الخطوات التالية

1. ✅ تشغيل `update_database.php`
2. ✅ اختبار التسجيل في `test_auth.html`
3. ✅ اختبار تسجيل الدخول في `test_auth.html`
4. ✅ اختبار التطبيق مع البيانات الجديدة

## الاتصال بالدعم

إذا استمرت المشاكل، يرجى إرسال:
1. رسالة الخطأ الكاملة
2. نتائج `update_database.php`
3. البيانات المستخدمة في التسجيل/تسجيل الدخول
4. لقطة شاشة من المشكلة
