<?php
// Debug script to test file upload functionality
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

function debugUploadFile($file, $uploadDir) {
    $debug = [];
    
    // Check if file was uploaded
    $debug['file_uploaded'] = isset($file);
    if (!isset($file)) {
        return ['success' => false, 'debug' => $debug, 'error' => 'No file uploaded'];
    }
    
    // Check upload error
    $debug['upload_error'] = $file['error'];
    $debug['upload_error_message'] = '';
    switch ($file['error']) {
        case UPLOAD_ERR_OK:
            $debug['upload_error_message'] = 'No error';
            break;
        case UPLOAD_ERR_INI_SIZE:
            $debug['upload_error_message'] = 'File too large (php.ini)';
            break;
        case UPLOAD_ERR_FORM_SIZE:
            $debug['upload_error_message'] = 'File too large (form)';
            break;
        case UPLOAD_ERR_PARTIAL:
            $debug['upload_error_message'] = 'Partial upload';
            break;
        case UPLOAD_ERR_NO_FILE:
            $debug['upload_error_message'] = 'No file uploaded';
            break;
        default:
            $debug['upload_error_message'] = 'Unknown error';
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'debug' => $debug, 'error' => $debug['upload_error_message']];
    }
    
    // File info
    $debug['file_name'] = $file['name'];
    $debug['file_size'] = $file['size'];
    $debug['file_type'] = $file['type'];
    $debug['file_tmp_name'] = $file['tmp_name'];
    
    // Check file type
    $allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
    $debug['allowed_types'] = $allowedTypes;
    $debug['type_allowed'] = in_array($file['type'], $allowedTypes);
    
    if (!$debug['type_allowed']) {
        return ['success' => false, 'debug' => $debug, 'error' => 'Invalid file type. Only JPEG and PNG are allowed.'];
    }
    
    // Check file size
    $maxSize = 5 * 1024 * 1024; // 5MB
    $debug['max_size'] = $maxSize;
    $debug['size_ok'] = $file['size'] <= $maxSize;
    
    if (!$debug['size_ok']) {
        return ['success' => false, 'debug' => $debug, 'error' => 'File size too large. Maximum 5MB allowed.'];
    }
    
    // Check upload directory
    $debug['upload_dir'] = $uploadDir;
    $debug['upload_dir_exists'] = is_dir($uploadDir);
    $debug['upload_dir_writable'] = is_writable($uploadDir);
    
    if (!$debug['upload_dir_exists']) {
        return ['success' => false, 'debug' => $debug, 'error' => 'Upload directory does not exist'];
    }
    
    if (!$debug['upload_dir_writable']) {
        return ['success' => false, 'debug' => $debug, 'error' => 'Upload directory is not writable'];
    }
    
    // Generate filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '.' . $extension;
    $uploadPath = $uploadDir . $filename;
    
    $debug['extension'] = $extension;
    $debug['filename'] = $filename;
    $debug['upload_path'] = $uploadPath;
    
    // Try to move file
    $debug['move_uploaded_file'] = move_uploaded_file($file['tmp_name'], $uploadPath);
    
    if (!$debug['move_uploaded_file']) {
        return ['success' => false, 'debug' => $debug, 'error' => 'Failed to move uploaded file'];
    }
    
    return ['success' => true, 'debug' => $debug, 'filename' => $filename];
}

// Test upload
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response = ['method' => 'POST'];
    
    // Debug $_FILES
    $response['files_info'] = $_FILES;
    
    // Debug $_POST
    $response['post_info'] = $_POST;
    
    if (isset($_FILES['image'])) {
        $result = debugUploadFile($_FILES['image'], './uploads/products/');
        $response = array_merge($response, $result);
    } else {
        $response['success'] = false;
        $response['error'] = 'No image file found in request';
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT);
} else {
    echo json_encode([
        'success' => false,
        'error' => 'Only POST method allowed',
        'method' => $_SERVER['REQUEST_METHOD']
    ], JSON_PRETTY_PRINT);
}
?>
