# دليل حل مشاكل رفع الملفات - AL Raid

## المشكلة الحالية
```
DioException [bad response]: Status code 500
Response: {"success":false,"message":"Internal server error occurred","debug":"Invalid file type. Only JPEG and PNG are allowed."}
```

## التحسينات المطبقة

### 1. في Flutter (al_raid_app/lib/services/file_service.dart)
- ✅ إضافة مكتبة `mime` لتحديد نوع المحتوى
- ✅ تحديث `toMultipartFile()` لإرسال `contentType` صحيح
- ✅ دعم أفضل للملفات على الويب والموبايل

### 2. في PHP (al_raid_backend/api/index.php)
- ✅ تحسين دالة `uploadFile()` للتحقق من:
  - امتداد الملف (.jpg, .jpeg, .png)
  - MIME type المرسل
  - المحتوى الفعلي باستخدام finfo
- ✅ إنشاء مجلدات الرفع تلقائياً
- ✅ رسائل خطأ أكثر وضوحاً

### 3. الأمان
- ✅ ملفات .htaccess لحماية مجلدات الرفع
- ✅ منع تنفيذ PHP في مجلدات الرفع
- ✅ منع الوصول المباشر للمجلدات

## خطوات التشخيص

### 1. فحص إعدادات PHP
افتح: `http://localhost/ALraid/al_raid_backend/check_php_settings.php`

تحقق من:
- `file_uploads`: يجب أن يكون `1`
- `upload_max_filesize`: يجب أن يكون `5M` أو أكثر
- `post_max_size`: يجب أن يكون `8M` أو أكثر
- صلاحيات مجلدات uploads

### 2. اختبار رفع الملفات
افتح: `http://localhost/ALraid/al_raid_backend/test_upload.html`

جرب رفع صورة JPEG أو PNG صغيرة (أقل من 5MB)

### 3. فحص مجلدات الرفع
تأكد من وجود المجلدات:
```
al_raid_backend/uploads/
al_raid_backend/uploads/products/
al_raid_backend/uploads/commercial_registers/
```

### 4. فحص صلاحيات المجلدات (Linux/Mac)
```bash
chmod 755 al_raid_backend/uploads/
chmod 755 al_raid_backend/uploads/products/
chmod 755 al_raid_backend/uploads/commercial_registers/
```

### 5. فحص صلاحيات المجلدات (Windows/XAMPP)
- تأكد من أن مجلد `htdocs` قابل للكتابة
- تأكد من تشغيل Apache بصلاحيات مناسبة

## اختبار التطبيق

### 1. تحديث التبعيات
```bash
cd al_raid_app
flutter pub get
```

### 2. تشغيل التطبيق
```bash
flutter run
```

### 3. اختبار رفع المنتج
- سجل دخول كتاجر
- اذهب لإضافة منتج جديد
- اختر صورة من المعرض أو الكاميرا
- املأ البيانات واضغط حفظ

## حلول المشاكل الشائعة

### مشكلة: "Upload directory is not writable"
**الحل:**
```bash
# Linux/Mac
chmod 755 al_raid_backend/uploads/
chown www-data:www-data al_raid_backend/uploads/

# Windows
# تأكد من تشغيل XAMPP كمدير
```

### مشكلة: "Invalid file type"
**الحل:**
- تأكد من أن الملف هو JPEG أو PNG
- تأكد من أن امتداد الملف صحيح
- جرب ملف صورة مختلف

### مشكلة: "File size too large"
**الحل:**
- تأكد من أن حجم الملف أقل من 5MB
- تحقق من إعدادات PHP:
  ```ini
  upload_max_filesize = 5M
  post_max_size = 8M
  ```

### مشكلة: "Failed to create upload directory"
**الحل:**
- تأكد من صلاحيات الكتابة في مجلد al_raid_backend
- أنشئ المجلدات يدوياً إذا لزم الأمر

## ملفات التشخيص المتاحة

1. `check_php_settings.php` - فحص إعدادات PHP
2. `debug_upload.php` - تشخيص مفصل لرفع الملفات
3. `test_upload.html` - اختبار رفع الملفات من المتصفح

## الاتصال بالدعم

إذا استمرت المشكلة، يرجى إرسال:
1. نتائج `check_php_settings.php`
2. رسالة الخطأ الكاملة من التطبيق
3. نوع وحجم الملف المحاول رفعه
4. نظام التشغيل المستخدم
