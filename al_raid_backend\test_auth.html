<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication - AL Raid</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background: #e9ecef;
            border: 1px solid #ddd;
            cursor: pointer;
            border-bottom: none;
        }
        .tab.active {
            background: white;
            border-bottom: 1px solid white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>Test Authentication - AL Raid</h1>
    
    <div class="tabs">
        <div class="tab active" onclick="showTab('register')">Register</div>
        <div class="tab" onclick="showTab('login')">Login</div>
        <div class="tab" onclick="showTab('existing')">Existing Users</div>
    </div>

    <!-- Register Tab -->
    <div id="register" class="tab-content active">
        <div class="container">
            <h2>Register New User</h2>
            <form id="registerForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="reg_full_name">Full Name:</label>
                    <input type="text" id="reg_full_name" name="full_name" required>
                </div>
                
                <div class="form-group">
                    <label for="reg_email">Email:</label>
                    <input type="email" id="reg_email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="reg_phone">Phone Number:</label>
                    <input type="tel" id="reg_phone" name="phone_number" required>
                </div>
                
                <div class="form-group">
                    <label for="reg_password">Password:</label>
                    <input type="password" id="reg_password" name="password" required minlength="6">
                </div>
                
                <div class="form-group">
                    <label for="reg_address">Shipping Address:</label>
                    <textarea id="reg_address" name="shipping_address" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="reg_country">Country:</label>
                    <select id="reg_country" name="country" required>
                        <option value="Saudi Arabia">Saudi Arabia</option>
                        <option value="United Arab Emirates">United Arab Emirates</option>
                        <option value="Kuwait">Kuwait</option>
                        <option value="Qatar">Qatar</option>
                        <option value="Bahrain">Bahrain</option>
                        <option value="Oman">Oman</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="reg_role">Role:</label>
                    <select id="reg_role" name="role" required>
                        <option value="buyer">Buyer</option>
                        <option value="merchant">Merchant</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="reg_commercial">Commercial Register (Optional):</label>
                    <input type="file" id="reg_commercial" name="commercial_register_photo" accept="image/*">
                </div>
                
                <button type="submit">Register</button>
            </form>
            <div id="registerResult"></div>
        </div>
    </div>

    <!-- Login Tab -->
    <div id="login" class="tab-content">
        <div class="container">
            <h2>Login</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="login_email">Email:</label>
                    <input type="email" id="login_email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="login_password">Password:</label>
                    <input type="password" id="login_password" name="password" required>
                </div>
                
                <button type="submit">Login</button>
            </form>
            <div id="loginResult"></div>
        </div>
    </div>

    <!-- Existing Users Tab -->
    <div id="existing" class="tab-content">
        <div class="container">
            <h2>Existing Test Users</h2>
            <p>You can use these accounts to test login (password: <code>123456</code>):</p>
            <ul>
                <li><strong><EMAIL></strong> - Merchant from Saudi Arabia</li>
                <li><strong><EMAIL></strong> - Buyer from UAE</li>
                <li><strong><EMAIL></strong> - Merchant from Qatar</li>
                <li><strong><EMAIL></strong> - Buyer from Kuwait</li>
            </ul>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // Register form
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('registerResult');
            
            try {
                resultDiv.innerHTML = '<p>Registering...</p>';
                
                const response = await fetch('./api/register', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `<div class="result success">
                        <h3>Registration Successful!</h3>
                        <p>${result.message}</p>
                        <p><strong>User ID:</strong> ${result.user.id}</p>
                        <p><strong>Email:</strong> ${result.user.email}</p>
                        <p><strong>Role:</strong> ${result.user.role}</p>
                    </div>`;
                    this.reset();
                } else {
                    resultDiv.innerHTML = `<div class="result error">
                        <h3>Registration Failed!</h3>
                        <p>${result.message}</p>
                    </div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">
                    <h3>Network Error!</h3>
                    <p>${error.message}</p>
                </div>`;
            }
        });

        // Login form
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            const resultDiv = document.getElementById('loginResult');
            
            try {
                resultDiv.innerHTML = '<p>Logging in...</p>';
                
                const response = await fetch('./api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `<div class="result success">
                        <h3>Login Successful!</h3>
                        <p>${result.message}</p>
                        <p><strong>Welcome:</strong> ${result.user.full_name}</p>
                        <p><strong>Email:</strong> ${result.user.email}</p>
                        <p><strong>Role:</strong> ${result.user.role}</p>
                        <p><strong>Country:</strong> ${result.user.country}</p>
                    </div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">
                        <h3>Login Failed!</h3>
                        <p>${result.message}</p>
                    </div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">
                    <h3>Network Error!</h3>
                    <p>${error.message}</p>
                </div>`;
            }
        });
    </script>
</body>
</html>
