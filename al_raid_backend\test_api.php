<?php
/**
 * API Test Script
 * This script tests the API endpoints to ensure they're working correctly
 */

echo "<h1>AL-Raid API Test</h1>";

$base_url = 'https://alraid.ridcod.com/api';
$local_url = 'http://localhost/ALraid/al_raid_backend/api';

// Determine which URL to use
$test_url = (isset($_GET['local']) && $_GET['local'] == '1') ? $local_url : $base_url;

echo "<p><strong>Testing URL:</strong> $test_url</p>";
echo "<p><a href='?local=1'>Test Local</a> | <a href='?'>Test Production</a></p>";

function testEndpoint($url, $name) {
    echo "<h3>Testing: $name</h3>";
    echo "<p><strong>URL:</strong> $url</p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p style='color: red;'>❌ CURL Error: $error</p>";
        return false;
    }
    
    echo "<p><strong>HTTP Code:</strong> $http_code</p>";
    
    if ($http_code == 200) {
        echo "<p style='color: green;'>✅ Success</p>";
        $data = json_decode($response, true);
        if ($data) {
            echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";
        } else {
            echo "<p>Response: $response</p>";
        }
        return true;
    } else {
        echo "<p style='color: red;'>❌ Failed</p>";
        echo "<p>Response: $response</p>";
        return false;
    }
}

// Test endpoints
$tests = [
    'Health Check' => $test_url . '/health',
    'All Products' => $test_url . '/products',
    'Industrial Products' => $test_url . '/products?type=industrial',
    'Food Products' => $test_url . '/products?type=food',
];

$passed = 0;
$total = count($tests);

foreach ($tests as $name => $url) {
    if (testEndpoint($url, $name)) {
        $passed++;
    }
    echo "<hr>";
}

echo "<h2>Test Results: $passed/$total tests passed</h2>";

if ($passed == $total) {
    echo "<p style='color: green; font-size: 18px;'>🎉 All tests passed! The API is working correctly.</p>";
} else {
    echo "<p style='color: red; font-size: 18px;'>⚠️ Some tests failed. Please check the issues above.</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}
h1, h2, h3 {
    color: #1976D2;
}
pre {
    background: #f8f8f8;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    border: 1px solid #ddd;
}
hr {
    margin: 20px 0;
    border: none;
    border-top: 2px solid #ddd;
}
a {
    color: #1976D2;
    text-decoration: none;
    padding: 5px 10px;
    background: white;
    border-radius: 3px;
    margin-right: 10px;
}
a:hover {
    background: #1976D2;
    color: white;
}
</style>
