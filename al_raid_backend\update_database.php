<?php
// Update existing database to add password column
require_once 'config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "<h2>Updating AL Raid Database</h2>";
    
    // Check if password column exists
    $stmt = $pdo->prepare("SHOW COLUMNS FROM users LIKE 'password'");
    $stmt->execute();
    $passwordColumnExists = $stmt->fetch();
    
    if (!$passwordColumnExists) {
        // Add password column
        $pdo->exec("ALTER TABLE users ADD COLUMN password VARCHAR(255) NOT NULL DEFAULT ''");
        echo "<p>✓ Password column added to users table</p>";
        
        // Update existing users with default password (123456)
        $defaultPassword = password_hash('123456', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE password = ''");
        $stmt->execute([$defaultPassword]);
        echo "<p>✓ Default passwords set for existing users (password: 123456)</p>";
    } else {
        echo "<p>✓ Password column already exists</p>";
    }
    
    // Verify table structure
    echo "<h3>Current Users Table Structure:</h3>";
    $stmt = $pdo->prepare("DESCRIBE users");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show existing users
    echo "<h3>Existing Users:</h3>";
    $stmt = $pdo->prepare("SELECT id, full_name, email, role, created_at FROM users");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Full Name</th><th>Email</th><th>Role</th><th>Created At</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['id']) . "</td>";
            echo "<td>" . htmlspecialchars($user['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "<td>" . htmlspecialchars($user['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p><strong>Note:</strong> All existing users now have the default password: <code>123456</code></p>";
    } else {
        echo "<p>No users found in database.</p>";
    }
    
    echo "<h3>✅ Database update completed successfully!</h3>";
    echo "<p>You can now:</p>";
    echo "<ul>";
    echo "<li>Register new users with passwords</li>";
    echo "<li>Login with existing users using password: <code>123456</code></li>";
    echo "<li>Test the registration and login functionality</li>";
    echo "</ul>";
    
} catch(PDOException $e) {
    echo "<h3 style='color: red;'>❌ Error updating database:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
}
?>
