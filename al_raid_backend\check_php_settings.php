<?php
// Check PHP settings for file upload
header('Content-Type: application/json');

$settings = [
    'file_uploads' => ini_get('file_uploads'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_execution_time' => ini_get('max_execution_time'),
    'memory_limit' => ini_get('memory_limit'),
    'upload_tmp_dir' => ini_get('upload_tmp_dir'),
];

$directories = [
    'uploads_exists' => is_dir('./uploads'),
    'uploads_writable' => is_writable('./uploads'),
    'products_exists' => is_dir('./uploads/products'),
    'products_writable' => is_writable('./uploads/products'),
    'commercial_registers_exists' => is_dir('./uploads/commercial_registers'),
    'commercial_registers_writable' => is_writable('./uploads/commercial_registers'),
];

$permissions = [];
if (is_dir('./uploads')) {
    $permissions['uploads'] = substr(sprintf('%o', fileperms('./uploads')), -4);
}
if (is_dir('./uploads/products')) {
    $permissions['products'] = substr(sprintf('%o', fileperms('./uploads/products')), -4);
}
if (is_dir('./uploads/commercial_registers')) {
    $permissions['commercial_registers'] = substr(sprintf('%o', fileperms('./uploads/commercial_registers')), -4);
}

echo json_encode([
    'php_settings' => $settings,
    'directories' => $directories,
    'permissions' => $permissions,
    'current_user' => get_current_user(),
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
], JSON_PRETTY_PRINT);
?>
