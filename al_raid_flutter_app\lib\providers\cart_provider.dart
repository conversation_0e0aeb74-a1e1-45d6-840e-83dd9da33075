import 'package:flutter/foundation.dart';
import '../models/product.dart';
import '../models/order.dart';

class CartProvider extends ChangeNotifier {
  final List<OrderItem> _cartItems = [];

  List<OrderItem> get cartItems => List.unmodifiable(_cartItems);

  int get itemCount => _cartItems.length;

  double get totalAmount {
    return _cartItems.fold(
      0.0,
      (sum, item) => sum + (item.product.price * item.quantity),
    );
  }

  bool get isEmpty => _cartItems.isEmpty;

  bool get isNotEmpty => _cartItems.isNotEmpty;  void addToCart(Product product, [int quantity = 1]) {
    final existingIndex = _cartItems.indexWhere(
      (item) => item.product.id == product.id,
    );

    if (existingIndex >= 0) {
      _cartItems[existingIndex] = OrderItem(
        id: _cartItems[existingIndex].id,
        product: product,
        quantity: _cartItems[existingIndex].quantity + quantity,
        price: product.price,
      );
    } else {
      _cartItems.add(
        OrderItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          product: product,
          quantity: quantity,
          price: product.price,
        ),
      );
    }

    notifyListeners();
  }
  void removeFromCart(String productId) {
    _cartItems.removeWhere((item) => item.product.id.toString() == productId);
    notifyListeners();
  }
  void updateQuantity(String productId, int quantity) {
    if (quantity <= 0) {
      removeFromCart(productId);
      return;
    }

    final index = _cartItems.indexWhere(
      (item) => item.product.id.toString() == productId,
    );

    if (index >= 0) {
      _cartItems[index] = OrderItem(
        id: _cartItems[index].id,
        product: _cartItems[index].product,
        quantity: quantity,
        price: _cartItems[index].price,
      );
      notifyListeners();
    }
  }

  void clearCart() {
    _cartItems.clear();
    notifyListeners();
  }
  bool isInCart(String productId) {
    return _cartItems.any((item) => item.product.id.toString() == productId);
  }

  int getQuantity(String productId) {
    try {
      final item = _cartItems.firstWhere(
        (item) => item.product.id.toString() == productId,
      );
      return item.quantity;
    } catch (e) {
      return 0;
    }
  }
}
