<?php
/**
 * Debug Script for AL-Raid Backend
 * This script helps diagnose issues with the backend setup
 */

echo "<h1>AL-Raid Backend Debug Information</h1>";

// PHP Information
echo "<h2>PHP Configuration</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Script Path:</strong> " . __FILE__ . "</p>";

// Check required extensions
echo "<h2>Required Extensions</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'json', 'curl'];
foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✅' : '❌';
    echo "<p>$status $ext</p>";
}

// Database Connection Test
echo "<h2>Database Connection Test</h2>";
require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p>✅ Database connection successful</p>";
        
        // Test tables
        $tables = ['users', 'products', 'orders', 'admins'];
        foreach ($tables as $table) {
            try {
                $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
                $result = $stmt->fetch();
                echo "<p>✅ Table '$table': {$result['count']} records</p>";
            } catch (Exception $e) {
                echo "<p>❌ Table '$table': Error - " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p>❌ Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
}

// File Permissions
echo "<h2>File Permissions</h2>";
$directories = ['uploads', 'uploads/products', 'uploads/commercial_registers'];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        $writable = is_writable($dir) ? '✅' : '❌';
        echo "<p>$writable Directory '$dir': $perms</p>";
    } else {
        echo "<p>❌ Directory '$dir': Not found</p>";
    }
}

// Server Variables
echo "<h2>Server Variables</h2>";
$important_vars = [
    'HTTP_HOST',
    'REQUEST_URI',
    'SCRIPT_NAME',
    'REQUEST_METHOD',
    'QUERY_STRING',
    'HTTP_ORIGIN',
    'HTTP_USER_AGENT'
];

foreach ($important_vars as $var) {
    $value = $_SERVER[$var] ?? 'Not set';
    echo "<p><strong>$var:</strong> $value</p>";
}

// Test API Endpoint
echo "<h2>API Endpoint Test</h2>";
$api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . '/api/health';
echo "<p><strong>Testing:</strong> <a href='$api_url' target='_blank'>$api_url</a></p>";

// Environment Detection
echo "<h2>Environment Detection</h2>";
$is_production = (isset($_SERVER['HTTP_HOST']) && strpos($_SERVER['HTTP_HOST'], 'ridcod.com') !== false);
echo "<p><strong>Environment:</strong> " . ($is_production ? 'Production' : 'Development') . "</p>";

// Error Log
echo "<h2>Recent Error Log</h2>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    $lines = file($error_log);
    $recent_lines = array_slice($lines, -10);
    echo "<pre>" . implode('', $recent_lines) . "</pre>";
} else {
    echo "<p>No error log found or accessible</p>";
}

echo "<h2>Recommendations</h2>";
echo "<ul>";
echo "<li>Ensure all required PHP extensions are installed</li>";
echo "<li>Check database credentials in config/database.php</li>";
echo "<li>Verify upload directories have write permissions</li>";
echo "<li>Test API endpoints using the test_api.php script</li>";
echo "<li>Check server error logs for detailed error messages</li>";
echo "</ul>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}
h1, h2 {
    color: #1976D2;
}
pre {
    background: #f8f8f8;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    border: 1px solid #ddd;
    max-height: 200px;
    overflow-y: auto;
}
ul {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
a {
    color: #1976D2;
}
</style>
