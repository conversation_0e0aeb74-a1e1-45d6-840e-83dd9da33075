<?php
class Database {
    private $host;
    private $db_name;
    private $username;
    private $password;
    private $conn;

    public function __construct() {
        // Check if we're in production or development
        if (isset($_SERVER['HTTP_HOST']) && strpos($_SERVER['HTTP_HOST'], 'ridcod.com') !== false) {
            // Production configuration
            $this->host = 'localhost';
            $this->db_name = 'ridcodco_al_raid_db';
            $this->username = 'ridcodco_alraid';
            $this->password = 'your_production_password'; // Update this with actual password
        } else {
            // Development configuration
            $this->host = 'localhost';
            $this->db_name = 'al_raid_db';
            $this->username = 'root';
            $this->password = '';
        }
    }

    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8",
                $this->username,
                $this->password,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ]
            );
            $this->conn->exec("set names utf8");
        } catch(PDOException $exception) {
            error_log("Database connection error: " . $exception->getMessage());
            return null;
        }

        return $this->conn;
    }
}

// Database creation script
function createDatabase() {
    try {
        $pdo = new PDO("mysql:host=localhost", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Create database
        $pdo->exec("CREATE DATABASE IF NOT EXISTS al_raid_db CHARACTER SET utf8 COLLATE utf8_general_ci");

        // Use the database
        $pdo->exec("USE al_raid_db");

        // Create users table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                full_name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                phone_number VARCHAR(50) NOT NULL,
                password VARCHAR(255) NOT NULL,
                commercial_register_photo VARCHAR(255),
                shipping_address TEXT NOT NULL,
                country VARCHAR(100) NOT NULL,
                role ENUM('merchant', 'buyer') NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");

        // Create products table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                image VARCHAR(255),
                description TEXT NOT NULL,
                country_of_origin VARCHAR(100) NOT NULL,
                price DECIMAL(10, 2) NOT NULL,
                type ENUM('industrial', 'food') NOT NULL,
                status ENUM('under_review', 'approved', 'rejected') DEFAULT 'under_review',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ");

        // Create orders table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS orders (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_id INT NOT NULL,
                buyer_id INT NOT NULL,
                status ENUM('under_review', 'approved', 'rejected') DEFAULT 'under_review',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                FOREIGN KEY (buyer_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ");

        // Create admin table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");

        // Insert default admin user (username: admin, password: admin123)
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->exec("
            INSERT IGNORE INTO admins (username, password)
            VALUES ('admin', '$adminPassword')
        ");

        echo "Database and tables created successfully!";

    } catch(PDOException $e) {
        echo "Error: " . $e->getMessage();
    }
}

// Uncomment the line below to create the database and tables
// createDatabase();
?>
