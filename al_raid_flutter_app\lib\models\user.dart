import 'dart:convert';

// إضافة UserType enum
enum UserType {
  merchant,
  buyer,
}

class User {
  final int id;
  final String fullName;
  final String email;
  final String phoneNumber;
  final String? commercialRegisterPhoto;
  final String shippingAddress;
  final String country;
  final String role; // 'merchant' or 'buyer'
  final DateTime createdAt;
  final DateTime? updatedAt;
  
  // إضافة الخصائص المفقودة
  final String? name;
  final String? phone;
  final UserType? userType;
  User({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phoneNumber,
    this.commercialRegisterPhoto,
    required this.shippingAddress,
    required this.country,
    required this.role,
    required this.createdAt,
    this.updatedAt,
    this.name,
    this.phone,
    this.userType,
  });
  User copyWith({
    int? id,
    String? fullName,
    String? email,
    String? phoneNumber,
    String? commercialRegisterPhoto,
    String? shippingAddress,
    String? country,
    String? role,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? name,
    String? phone,
    UserType? userType,
  }) {
    return User(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      commercialRegisterPhoto: commercialRegisterPhoto ?? this.commercialRegisterPhoto,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      country: country ?? this.country,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      userType: userType ?? this.userType,
    );
  }
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id']?.toInt() ?? 0,
      fullName: map['full_name'] ?? '',
      email: map['email'] ?? '',
      phoneNumber: map['phone_number'] ?? '',
      commercialRegisterPhoto: map['commercial_register_photo'],
      shippingAddress: map['shipping_address'] ?? '',
      country: map['country'] ?? '',
      role: map['role'] ?? '',
      createdAt: DateTime.tryParse(map['created_at'] ?? '') ?? DateTime.now(),
      updatedAt: map['updated_at'] != null ? DateTime.tryParse(map['updated_at']) : null,
      name: map['name'],
      phone: map['phone'],
      userType: map['user_type'] != null ? UserType.values.firstWhere(
        (e) => e.toString().split('.').last == map['user_type'],
        orElse: () => UserType.buyer,
      ) : null,
    );
  }

  factory User.fromJson(String source) => User.fromMap(json.decode(source));

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'full_name': fullName,
      'email': email,
      'phone_number': phoneNumber,
      'commercial_register_photo': commercialRegisterPhoto,
      'shipping_address': shippingAddress,
      'country': country,
      'role': role,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());

  @override
  String toString() {
    return 'User(id: $id, fullName: $fullName, email: $email, phoneNumber: $phoneNumber, commercialRegisterPhoto: $commercialRegisterPhoto, shippingAddress: $shippingAddress, country: $country, role: $role, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is User &&
      other.id == id &&
      other.fullName == fullName &&
      other.email == email &&
      other.phoneNumber == phoneNumber &&
      other.commercialRegisterPhoto == commercialRegisterPhoto &&
      other.shippingAddress == shippingAddress &&
      other.country == country &&
      other.role == role &&
      other.createdAt == createdAt &&
      other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      fullName.hashCode ^
      email.hashCode ^
      phoneNumber.hashCode ^
      commercialRegisterPhoto.hashCode ^
      shippingAddress.hashCode ^
      country.hashCode ^
      role.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode;
  }

  bool get isMerchant => role == 'merchant';
  bool get isBuyer => role == 'buyer';
}
