name: al_raid_app
description: Modern e-commerce platform for showcasing and marketing industrial and food products with glassmorphism UI design.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # Navigation
  go_router: ^12.1.3
  
  # UI & Design
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_animate: ^4.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  
  # State Management
  provider: ^6.1.1
  
  # Networking
  http: ^1.1.0
  dio: ^5.3.3
  
  # Images & Media
  cached_network_image: ^3.3.0
  image_picker: ^1.0.4
  flutter_image_compress: ^2.1.0
  
  # Storage
  shared_preferences: ^2.2.2
  
  # Utilities
  intl: 0.20.2
  path_provider: ^2.1.1
  url_launcher: ^6.2.1
  permission_handler: ^11.0.1
  
  # Forms & Validation
  email_validator: ^2.1.17
  
  # UI Components
  flutter_staggered_grid_view: ^0.7.0
  flutter_spinkit: ^5.2.0
  fluttertoast: ^8.2.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  
  # Generate localization files
  generate: true
  
  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
  
  # Fonts
  fonts:
    - family: Tajawal
      fonts:
        - asset: assets/fonts/Tajawal-Regular.ttf
        - asset: assets/fonts/Tajawal-Bold.ttf
          weight: 700
        - asset: assets/fonts/Tajawal-Medium.ttf
          weight: 500
        - asset: assets/fonts/Tajawal-Light.ttf
          weight: 300
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-Light.ttf
          weight: 300

# Flutter build configuration for size optimization
flutter_build:
  # Tree shaking
  tree-shake-icons: true
  # Split debug info
  split-debug-info: build/app/outputs/symbols/
  # Obfuscate code
  obfuscate: true
