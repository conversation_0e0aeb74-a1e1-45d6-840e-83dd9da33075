import 'package:flutter/material.dart';

class ThemeProvider with ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  
  ThemeMode get themeMode => _themeMode;
  
  bool get isDarkMode => _themeMode == ThemeMode.dark;
  bool get isLightMode => _themeMode == ThemeMode.light;
  bool get isSystemMode => _themeMode == ThemeMode.system;

  void setThemeMode(ThemeMode themeMode) {
    _themeMode = themeMode;
    notifyListeners();
    _saveThemeMode(themeMode);
  }

  void toggleTheme() {
    if (_themeMode == ThemeMode.light) {
      setThemeMode(ThemeMode.dark);
    } else {
      setThemeMode(ThemeMode.light);
    }
  }

  void _saveThemeMode(ThemeMode themeMode) async {
    // Save to SharedPreferences
    // final prefs = await SharedPreferences.getInstance();
    // await prefs.setString('theme_mode', themeMode.toString());
  }

  void loadThemeMode() async {
    // Load from SharedPreferences
    // final prefs = await SharedPreferences.getInstance();
    // final themeModeString = prefs.getString('theme_mode');
    // if (themeModeString != null) {
    //   _themeMode = ThemeMode.values.firstWhere(
    //     (mode) => mode.toString() == themeModeString,
    //     orElse: () => ThemeMode.system,
    //   );
    //   notifyListeners();
    // }
  }
}
