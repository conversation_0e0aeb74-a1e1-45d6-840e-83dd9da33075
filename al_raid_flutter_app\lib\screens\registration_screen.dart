import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'dart:math' as math;
import '../providers/auth_provider.dart';
import '../providers/language_provider.dart';
import '../widgets/glassmorphic_card.dart';
import '../widgets/gradient_button.dart';
import '../widgets/animated_input_field.dart';

class RegistrationScreen extends StatefulWidget {
  const RegistrationScreen({Key? key}) : super(key: key);

  @override
  State<RegistrationScreen> createState() => _RegistrationScreenState();
}

class _RegistrationScreenState extends State<RegistrationScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _addressController = TextEditingController();

  late AnimationController _mainAnimationController;
  late AnimationController _backgroundAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _acceptTerms = false;
  String _selectedUserType = 'buyer'; // buyer or merchant
  String? _selectedCountry;

  @override
  void initState() {
    super.initState();

    _mainAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _backgroundAnimationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _mainAnimationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _mainAnimationController,
        curve: const Interval(0.3, 1.0, curve: Curves.elasticOut),
      ),
    );

    _mainAnimationController.forward();
  }

  @override
  void dispose() {
    _mainAnimationController.dispose();
    _backgroundAnimationController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _handleRegistration() async {
    if (_formKey.currentState?.validate() ?? false) {
      if (!_acceptTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<LanguageProvider>(context, listen: false).isArabic
                  ? 'يرجى الموافقة على الشروط والأحكام'
                  : 'Please accept terms and conditions',
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
        return;
      }

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final languageProvider =
          Provider.of<LanguageProvider>(context, listen: false);
      final isRTL = languageProvider.isArabic;

      final success = await authProvider.register(
        fullName: _nameController.text.trim(),
        email: _emailController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        password: _passwordController.text,
        shippingAddress: _addressController.text.trim(),
        country: _selectedCountry ?? 'Unknown',
        role: _selectedUserType == 'merchant' ? 'merchant' : 'buyer',
      );

      if (success && authProvider.isAuthenticated && mounted) {
        context.go('/home');
      } else if (!success && mounted) {
        // Show error message from AuthProvider
        final errorMessage = authProvider.error ??
            (isRTL ? 'فشل في إنشاء الحساب' : 'Registration failed');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final size = MediaQuery.of(context).size;
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isRTL = languageProvider.isArabic;

    return Scaffold(
      body: Stack(
        children: [
          // Animated Background
          AnimatedBuilder(
            animation: _backgroundAnimationController,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    transform: GradientRotation(
                      _backgroundAnimationController.value * 2 * 3.14159,
                    ),
                    colors: isDark
                        ? [
                            const Color(0xFF1A1A2E),
                            const Color(0xFF16213E),
                            const Color(0xFF0F4C75),
                          ]
                        : [
                            const Color(0xFFE8F5E8),
                            const Color(0xFFC8E6C9),
                            const Color(0xFFA5D6A7),
                          ],
                  ),
                ),
              );
            },
          ),

          // Floating Elements
          Positioned(
            top: size.height * 0.1,
            right: size.width * 0.8,
            child: _buildFloatingElement(60, Colors.white.withOpacity(0.1)),
          ),
          Positioned(
            top: size.height * 0.4,
            left: size.width * 0.1,
            child: _buildFloatingElement(40, Colors.white.withOpacity(0.05)),
          ),
          Positioned(
            bottom: size.height * 0.1,
            right: size.width * 0.2,
            child: _buildFloatingElement(80, Colors.white.withOpacity(0.08)),
          ),

          // Main Content
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
              child: AnimatedBuilder(
                animation: _mainAnimationController,
                builder: (context, child) {
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Back Button and Language Toggle
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              IconButton(
                                onPressed: () => context.pop(),
                                icon: Icon(
                                  isRTL
                                      ? Icons.arrow_forward
                                      : Icons.arrow_back,
                                  color: Colors.white70,
                                ),
                              ),
                              TextButton(
                                onPressed: () {
                                  languageProvider.toggleLanguage();
                                },
                                child: Text(
                                  isRTL ? 'English' : 'العربية',
                                  style: const TextStyle(
                                    color: Colors.white70,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          SizedBox(height: size.height * 0.04),

                          // Title
                          Center(
                            child: Column(
                              children: [
                                Text(
                                  isRTL ? 'إنشاء حساب جديد' : 'Create Account',
                                  style: TextStyle(
                                    fontSize: 28,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    shadows: [
                                      Shadow(
                                        color: Colors.black.withOpacity(
                                          0.3,
                                        ),
                                        blurRadius: 10,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                )
                                    .animate()
                                    .fadeIn(delay: 200.ms, duration: 800.ms)
                                    .slideY(begin: 0.3, end: 0),
                                const SizedBox(height: 8),
                                Text(
                                  isRTL
                                      ? 'انضم إلى منصة التجارة الإلكترونية'
                                      : 'Join our e-commerce platform',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.white70,
                                    shadows: [
                                      Shadow(
                                        color: Colors.black.withOpacity(0.3),
                                        blurRadius: 5,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                ).animate().fadeIn(
                                      delay: 400.ms,
                                      duration: 800.ms,
                                    ),
                              ],
                            ),
                          ),

                          SizedBox(height: size.height * 0.04),

                          // Registration Form
                          GlassmorphicCard(
                            child: Form(
                              key: _formKey,
                              child: Column(
                                children: [
                                  // User Type Selection
                                  Text(
                                    isRTL ? 'نوع الحساب' : 'Account Type',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: isDark
                                          ? Colors.white
                                          : Colors.grey.shade800,
                                    ),
                                  ),
                                  const SizedBox(height: 12),

                                  Row(
                                    children: [
                                      Expanded(
                                        child: GestureDetector(
                                          onTap: () {
                                            setState(() {
                                              _selectedUserType = 'buyer';
                                            });
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.all(16),
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              border: Border.all(
                                                color:
                                                    _selectedUserType == 'buyer'
                                                        ? theme.primaryColor
                                                        : Colors.grey.shade300,
                                                width: 2,
                                              ),
                                              color:
                                                  _selectedUserType == 'buyer'
                                                      ? theme.primaryColor
                                                          .withOpacity(0.1)
                                                      : null,
                                            ),
                                            child: Column(
                                              children: [
                                                Icon(
                                                  Icons.shopping_cart,
                                                  color: _selectedUserType ==
                                                          'buyer'
                                                      ? theme.primaryColor
                                                      : Colors.grey.shade600,
                                                  size: 32,
                                                ),
                                                const SizedBox(height: 8),
                                                Text(
                                                  isRTL ? 'مشتري' : 'Buyer',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w600,
                                                    color: _selectedUserType ==
                                                            'buyer'
                                                        ? theme.primaryColor
                                                        : Colors.grey.shade600,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: GestureDetector(
                                          onTap: () {
                                            setState(() {
                                              _selectedUserType = 'merchant';
                                            });
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.all(16),
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              border: Border.all(
                                                color: _selectedUserType ==
                                                        'merchant'
                                                    ? theme.primaryColor
                                                    : Colors.grey.shade300,
                                                width: 2,
                                              ),
                                              color: _selectedUserType ==
                                                      'merchant'
                                                  ? theme.primaryColor
                                                      .withOpacity(0.1)
                                                  : null,
                                            ),
                                            child: Column(
                                              children: [
                                                Icon(
                                                  Icons.store,
                                                  color: _selectedUserType ==
                                                          'merchant'
                                                      ? theme.primaryColor
                                                      : Colors.grey.shade600,
                                                  size: 32,
                                                ),
                                                const SizedBox(height: 8),
                                                Text(
                                                  isRTL ? 'تاجر' : 'Merchant',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w600,
                                                    color: _selectedUserType ==
                                                            'merchant'
                                                        ? theme.primaryColor
                                                        : Colors.grey.shade600,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 24),

                                  AnimatedInputField(
                                    labelText:
                                        isRTL ? 'الاسم الكامل' : 'Full Name',
                                    hintText: isRTL
                                        ? 'أدخل اسمك الكامل'
                                        : 'Enter your full name',
                                    controller: _nameController,
                                    prefixIcon: const Icon(
                                      Icons.person_outline,
                                    ),
                                    validator: (value) {
                                      if (value?.isEmpty ?? true) {
                                        return isRTL
                                            ? 'يرجى إدخال الاسم الكامل'
                                            : 'Please enter your full name';
                                      }
                                      if (value!.length < 2) {
                                        return isRTL
                                            ? 'الاسم يجب أن يكون حرفين على الأقل'
                                            : 'Name must be at least 2 characters';
                                      }
                                      return null;
                                    },
                                    isRequired: true,
                                  ),

                                  AnimatedInputField(
                                    labelText:
                                        isRTL ? 'البريد الإلكتروني' : 'Email',
                                    hintText: isRTL
                                        ? 'أدخل بريدك الإلكتروني'
                                        : 'Enter your email',
                                    controller: _emailController,
                                    keyboardType: TextInputType.emailAddress,
                                    prefixIcon: const Icon(
                                      Icons.email_outlined,
                                    ),
                                    validator: (value) {
                                      if (value?.isEmpty ?? true) {
                                        return isRTL
                                            ? 'يرجى إدخال البريد الإلكتروني'
                                            : 'Please enter your email';
                                      }
                                      if (!RegExp(
                                        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                      ).hasMatch(value!)) {
                                        return isRTL
                                            ? 'يرجى إدخال بريد إلكتروني صحيح'
                                            : 'Please enter a valid email';
                                      }
                                      return null;
                                    },
                                    isRequired: true,
                                  ),

                                  AnimatedInputField(
                                    labelText:
                                        isRTL ? 'رقم الهاتف' : 'Phone Number',
                                    hintText: isRTL
                                        ? 'أدخل رقم هاتفك'
                                        : 'Enter your phone number',
                                    controller: _phoneController,
                                    keyboardType: TextInputType.phone,
                                    prefixIcon: const Icon(
                                      Icons.phone_outlined,
                                    ),
                                    validator: (value) {
                                      if (value?.isEmpty ?? true) {
                                        return isRTL
                                            ? 'يرجى إدخال رقم الهاتف'
                                            : 'Please enter your phone number';
                                      }
                                      if (value!.length < 10) {
                                        return isRTL
                                            ? 'رقم الهاتف يجب أن يكون 10 أرقام على الأقل'
                                            : 'Phone number must be at least 10 digits';
                                      }
                                      return null;
                                    },
                                    isRequired: true,
                                  ),

                                  AnimatedInputField(
                                    labelText:
                                        isRTL ? 'كلمة المرور' : 'Password',
                                    hintText: isRTL
                                        ? 'أدخل كلمة المرور'
                                        : 'Enter your password',
                                    controller: _passwordController,
                                    obscureText: !_isPasswordVisible,
                                    prefixIcon: const Icon(Icons.lock_outline),
                                    suffixIcon: IconButton(
                                      icon: Icon(
                                        _isPasswordVisible
                                            ? Icons.visibility_off
                                            : Icons.visibility,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isPasswordVisible =
                                              !_isPasswordVisible;
                                        });
                                      },
                                    ),
                                    validator: (value) {
                                      if (value?.isEmpty ?? true) {
                                        return isRTL
                                            ? 'يرجى إدخال كلمة المرور'
                                            : 'Please enter your password';
                                      }
                                      if (value!.length < 6) {
                                        return isRTL
                                            ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
                                            : 'Password must be at least 6 characters';
                                      }
                                      return null;
                                    },
                                    isRequired: true,
                                  ),

                                  AnimatedInputField(
                                    labelText: isRTL
                                        ? 'تأكيد كلمة المرور'
                                        : 'Confirm Password',
                                    hintText: isRTL
                                        ? 'أعد إدخال كلمة المرور'
                                        : 'Re-enter your password',
                                    controller: _confirmPasswordController,
                                    obscureText: !_isConfirmPasswordVisible,
                                    prefixIcon: const Icon(Icons.lock_outline),
                                    suffixIcon: IconButton(
                                      icon: Icon(
                                        _isConfirmPasswordVisible
                                            ? Icons.visibility_off
                                            : Icons.visibility,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isConfirmPasswordVisible =
                                              !_isConfirmPasswordVisible;
                                        });
                                      },
                                    ),
                                    validator: (value) {
                                      if (value?.isEmpty ?? true) {
                                        return isRTL
                                            ? 'يرجى تأكيد كلمة المرور'
                                            : 'Please confirm your password';
                                      }
                                      if (value != _passwordController.text) {
                                        return isRTL
                                            ? 'كلمة المرور غير متطابقة'
                                            : 'Passwords do not match';
                                      }
                                      return null;
                                    },
                                    isRequired: true,
                                  ),

                                  // Terms and Conditions
                                  Row(
                                    children: [
                                      Checkbox(
                                        value: _acceptTerms,
                                        onChanged: (value) {
                                          setState(() {
                                            _acceptTerms = value ?? false;
                                          });
                                        },
                                        activeColor: theme.primaryColor,
                                      ),
                                      Expanded(
                                        child: Text(
                                          isRTL
                                              ? 'أوافق على الشروط والأحكام وسياسة الخصوصية'
                                              : 'I agree to the Terms & Conditions and Privacy Policy',
                                          style: TextStyle(
                                            color: isDark
                                                ? Colors.white70
                                                : Colors.grey.shade700,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 24),

                                  // Register Button
                                  Consumer<AuthProvider>(
                                    builder: (context, authProvider, child) {
                                      return GradientButton(
                                        text: isRTL
                                            ? 'إنشاء الحساب'
                                            : 'Create Account',
                                        onPressed: _handleRegistration,
                                        isLoading: authProvider.isLoading,
                                        width: double.infinity,
                                        icon: const Icon(
                                          Icons.person_add,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),
                          )
                              .animate()
                              .fadeIn(delay: 600.ms, duration: 800.ms)
                              .slideY(begin: 0.2, end: 0),

                          const SizedBox(height: 24),

                          // Sign In Link
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                isRTL
                                    ? 'لديك حساب بالفعل؟ '
                                    : "Already have an account? ",
                                style: TextStyle(
                                  color: Colors.white70,
                                  fontSize: 16,
                                ),
                              ),
                              TextButton(
                                onPressed: () => context.pop(),
                                child: Text(
                                  isRTL ? 'تسجيل الدخول' : 'Sign In',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ).animate().fadeIn(delay: 800.ms, duration: 800.ms),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingElement(double size, Color color) {
    return AnimatedBuilder(
      animation: _backgroundAnimationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            20 * math.sin(_backgroundAnimationController.value * 2 * 3.14159),
            20 * math.cos(_backgroundAnimationController.value * 2 * 3.14159),
          ),
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(shape: BoxShape.circle, color: color),
          ),
        );
      },
    );
  }
}
