import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../providers/order_provider.dart';
import '../providers/auth_provider.dart';
import '../providers/language_provider.dart';
import '../models/order.dart';
import '../widgets/glassmorphic_card.dart';
import '../widgets/gradient_button.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({super.key});

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> {
  String _selectedFilter = 'all'; // all, under_review, approved, rejected

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadOrders();
    });
  }

  void _loadOrders() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final orderProvider = Provider.of<OrderProvider>(context, listen: false);

    if (authProvider.user != null) {
      orderProvider.loadUserOrders(authProvider.user!.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isRTL = languageProvider.isArabic;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary.withValues(alpha: 0.1),
              theme.colorScheme.secondary.withValues(alpha: 0.05),
              theme.colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.arrow_back_ios),
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.white.withValues(alpha: 0.1),
                        foregroundColor: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        isRTL ? 'طلباتي' : 'My Orders',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                    // Filter Button
                    PopupMenuButton<String>(
                      initialValue: _selectedFilter,
                      onSelected: (value) {
                        setState(() {
                          _selectedFilter = value;
                        });
                      },
                      icon: Icon(
                        Icons.filter_list,
                        color: theme.colorScheme.onSurface,
                      ),
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'all',
                          child: Text(isRTL ? 'جميع الطلبات' : 'All Orders'),
                        ),
                        PopupMenuItem(
                          value: 'under_review',
                          child: Text(isRTL ? 'قيد المراجعة' : 'Under Review'),
                        ),
                        PopupMenuItem(
                          value: 'approved',
                          child: Text(isRTL ? 'مقبولة' : 'Approved'),
                        ),
                        PopupMenuItem(
                          value: 'rejected',
                          child: Text(isRTL ? 'مرفوضة' : 'Rejected'),
                        ),
                      ],
                    ),
                  ],
                ),
              ).animate().fadeIn(duration: 600.ms).slideX(begin: -0.2),

              // Content
              Expanded(
                child: Consumer<OrderProvider>(
                  builder: (context, orderProvider, child) {
                    if (orderProvider.isLoading) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    if (orderProvider.error != null) {
                      return _buildErrorState(orderProvider.error!, isRTL);
                    }

                    final allOrders = orderProvider.orders;
                    final filteredOrders = _selectedFilter == 'all'
                        ? allOrders
                        : allOrders
                            .where((order) => order.status == _selectedFilter)
                            .toList();

                    if (filteredOrders.isEmpty) {
                      return _buildEmptyOrders(isRTL);
                    }

                    return _buildOrdersList(filteredOrders, isRTL);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyOrders(bool isRTL) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 100,
            color:
                Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 24),
          Text(
            isRTL ? 'لا توجد طلبات' : 'No orders yet',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.6),
                ),
          ),
          const SizedBox(height: 8),
          Text(
            isRTL
                ? 'ابدأ بطلب منتجاتك المفضلة'
                : 'Start ordering your favorite products',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.5),
                ),
          ),
          const SizedBox(height: 32),
          GradientButton(
            onPressed: () => Navigator.of(context).pop(),
            text: isRTL ? 'تصفح المنتجات' : 'Browse Products',
            icon: const Icon(
              Icons.shopping_bag,
              color: Colors.white,
              size: 20,
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).scale(begin: const Offset(0.8, 0.8));
  }

  Widget _buildErrorState(String error, bool isRTL) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 100,
            color: Colors.red.shade400,
          ),
          const SizedBox(height: 24),
          Text(
            isRTL ? 'خطأ في تحميل الطلبات' : 'Error loading orders',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.red.shade600,
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade600,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          GradientButton(
            onPressed: _loadOrders,
            text: isRTL ? 'إعادة المحاولة' : 'Retry',
            icon: const Icon(
              Icons.refresh,
              color: Colors.white,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList(List<Order> orders, bool isRTL) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return _buildOrderItem(order, index, isRTL);
      },
    );
  }

  Widget _buildOrderItem(Order order, int index, bool isRTL) {
    final theme = Theme.of(context);

    Color getStatusColor(String status) {
      switch (status) {
        case 'approved':
          return Colors.green;
        case 'rejected':
          return Colors.red;
        case 'under_review':
        default:
          return Colors.orange;
      }
    }

    String getStatusText(String status) {
      switch (status) {
        case 'approved':
          return isRTL ? 'مقبول' : 'Approved';
        case 'rejected':
          return isRTL ? 'مرفوض' : 'Rejected';
        case 'under_review':
        default:
          return isRTL ? 'قيد المراجعة' : 'Under Review';
      }
    }

    return GlassmorphicCard(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${isRTL ? 'طلب رقم' : 'Order'} #${order.id}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: getStatusColor(order.status).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  getStatusText(order.status),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: getStatusColor(order.status),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Product Info
          Row(
            children: [
              // Product Image
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                ),
                child: order.productImage != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: CachedNetworkImage(
                          imageUrl:
                              'https://alraid.ridcod.com/uploads/${order.productImage}',
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) => Icon(
                            Icons.image,
                            color: theme.colorScheme.primary,
                            size: 24,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.image,
                        color: theme.colorScheme.primary,
                        size: 24,
                      ),
              ),

              const SizedBox(width: 12),

              // Product Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      order.productDescription ?? 'Unknown Product',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: theme.colorScheme.onSurface,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    if (order.merchantName != null)
                      Text(
                        '${isRTL ? 'التاجر:' : 'Merchant:'} ${order.merchantName}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    const SizedBox(height: 4),
                    if (order.price != null)
                      Text(
                        '\$${order.price!.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: theme.primaryColor,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Order Date
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 4),
              Text(
                '${isRTL ? 'تاريخ الطلب:' : 'Order Date:'} ${_formatDate(order.createdAt)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ],
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 600.ms)
        .slideX(begin: 0.2);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
