Design and develop a modern, sleek Flutter mobile application with an elegant user interface, following 2025 design trends including glassmorphism and neumorphism effects. The app should use Tajawal Arabic font, maintain a final APK size under 50MB, and integrate with the existing REST API (al_raid_backend folder).

**Application Purpose**: E-commerce platform for showcasing and marketing industrial and food products.

**Technical Requirements**:
- Flutter framework with Material Design 3
- Modern UI components with glassmorphism/neumorphism effects
- Tajawal Arabic font for Arabic text, Inter/Roboto for English
- Responsive design for all screen sizes (mobile, tablet)
- HTTP package for API integration
- Image optimization and caching
- Final APK size < 50MB
- Support for Android and iOS
- RTL (Right-to-Left) language support
- Smooth animations and transitions

**Screen Specifications**:

1. **Splash/Terms Screen**:
   - Display terms of service with modern card layout
   - "Continue" button with gradient styling
   - Smooth transition to main app

2. **Home Screen**:
   - Modern product grid with glassmorphism cards
   - Each product card includes: product image, brief description, price, country of origin, "View Details" button
   - Pull-to-refresh functionality
   - Search functionality with modern search bar
   - Category filter chips (Industrial/Food)

3. **Product Categories**:
   - Industrial Products section with dedicated filtering
   - Food Products section with dedicated filtering
   - API endpoint filtering by product type
   - Modern category cards with icons and gradients

4. **Product Details Screen**:
   - Hero image animation
   - Product image gallery (if multiple images)
   - Detailed product description
   - Country of origin with flag icon
   - Price display with currency formatting
   - "Request Product" button with loading states
   - Order submission creates pending review status via API

5. **Authentication Screens**:
   
   **Login Screen**:
   - Email input field with validation
   - Password input field with show/hide toggle
   - "Login" button with loading animation
   - "Create Account" navigation link
   - Modern form design with glassmorphism effects

   **Registration Screen**:
   - Full Name input field
   - Email input field with validation
   - Phone Number input field with country code
   - Commercial Register Photo upload with image picker
   - Complete Shipping Address text area
   - Country dropdown with search functionality
   - Password input with strength indicator
   - Confirm Password field
   - "Register" button with validation feedback

6. **Profile Screen**:
   - User information display with modern cards
   - Edit profile functionality
   - "Logout" button with confirmation dialog
   - Account status indicator

7. **Add Product Screen** (Merchant only):
   - Product image upload with multiple image support
   - Product description rich text editor
   - Country of origin dropdown
   - Price input with currency selection
   - Product type selection (Industrial/Food) with radio buttons
   - "Submit for Review" button
   - Form validation and error handling

8. **Order Management**:

   **Merchant Orders Screen**:
   - List of orders received for merchant's products
   - Order cards showing: buyer name, requested product, order status, buyer contact info
   - Filter by order status (Pending/Approved/Rejected)
   - Only show orders after admin approval (status: approved)
   - Modern list design with status badges

   **Buyer Orders Screen**:
   - List of orders placed by the buyer
   - Order cards showing: product name, merchant name, price, order status, merchant contact info
   - Order tracking with status timeline
   - Filter and search functionality

9. **Status Management**:
   - Three status types: Under Review (Pending), Approved, Rejected
   - Color-coded status badges with modern design
   - Real-time status updates from API
   - Status change animations

**UI/UX Design Requirements**:
- Modern 2025 color palette with soft gradients
- Glassmorphism effects for cards and navigation
- Subtle neumorphism for buttons and interactive elements
- Smooth page transitions and micro-animations
- Bold typography hierarchy with Tajawal Arabic font
- Consistent spacing using 8px grid system
- Modern bottom navigation with glassmorphism
- Floating action buttons where appropriate
- Loading states and skeleton screens
- Error handling with user-friendly messages
- Dark/light theme support (optional)

**API Integration Requirements**:
- Connect to existing al_raid_backend REST API
- Implement proper error handling and retry logic
- Use HTTP package for network requests
- Implement caching for offline functionality
- Image upload handling for product and profile photos
- Authentication token management
- API response parsing and validation

**Performance Optimization**:
- Image compression and caching
- Lazy loading for product lists
- Efficient state management (Provider/Riverpod)
- Minimal package dependencies
- Code splitting and tree shaking
- Asset optimization
- Memory management for image handling

**Accessibility & Localization**:
- Support for Arabic (RTL) and English (LTR)
- Proper semantic labels for screen readers
- High contrast color ratios
- Touch target sizes (minimum 44px)
- Keyboard navigation support
- Text scaling support

**Testing Requirements**:
- Unit tests for business logic
- Widget tests for UI components
- Integration tests for API calls
- Performance testing for large product lists
- Cross-platform testing (Android/iOS)

Deliver a production-ready Flutter application with modern UI design, optimal performance, and seamless user experience that showcases industrial and food products effectively while maintaining the specified size constraints.