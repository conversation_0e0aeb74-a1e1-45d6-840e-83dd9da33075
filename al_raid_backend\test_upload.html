<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test File Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Test Product Upload</h1>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <div class="form-group">
            <label for="description">Product Description:</label>
            <textarea id="description" name="description" required>Test Product Description</textarea>
        </div>
        
        <div class="form-group">
            <label for="country_of_origin">Country of Origin:</label>
            <input type="text" id="country_of_origin" name="country_of_origin" value="Saudi Arabia" required>
        </div>
        
        <div class="form-group">
            <label for="price">Price:</label>
            <input type="number" id="price" name="price" step="0.01" value="100.00" required>
        </div>
        
        <div class="form-group">
            <label for="type">Product Type:</label>
            <select id="type" name="type" required>
                <option value="industrial">Industrial</option>
                <option value="food">Food</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="user_id">User ID:</label>
            <input type="number" id="user_id" name="user_id" value="1" required>
        </div>
        
        <div class="form-group">
            <label for="image">Product Image:</label>
            <input type="file" id="image" name="image" accept="image/jpeg,image/png,image/jpg" required>
        </div>
        
        <button type="submit">Upload Product</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.innerHTML = '<p>Uploading...</p>';
                
                const response = await fetch('./api/products', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `<div class="result success">
                        <h3>Success!</h3>
                        <p>${result.message}</p>
                    </div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">
                        <h3>Error!</h3>
                        <p>${result.message}</p>
                        ${result.debug ? `<pre>${JSON.stringify(result.debug, null, 2)}</pre>` : ''}
                    </div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">
                    <h3>Network Error!</h3>
                    <p>${error.message}</p>
                </div>`;
            }
        });
    </script>
</body>
</html>
