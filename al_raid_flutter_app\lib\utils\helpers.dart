import 'package:intl/intl.dart';

class AppHelpers {
  // Format currency
  static String formatCurrency(double amount, {String currency = 'SAR'}) {
    final formatter = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: currency,
      decimalDigits: 2,
    );
    return formatter.format(amount);
  }

  // Format date
  static String formatDate(DateTime date, {String locale = 'en'}) {
    if (locale == 'ar') {
      return DateFormat('dd/MM/yyyy', 'ar').format(date);
    }
    return DateFormat('dd/MM/yyyy').format(date);
  }

  // Format date with time
  static String formatDateTime(DateTime date, {String locale = 'en'}) {
    if (locale == 'ar') {
      return DateFormat('dd/MM/yyyy HH:mm', 'ar').format(date);
    }
    return DateFormat('dd/MM/yyyy HH:mm').format(date);
  }

  // Format time only
  static String formatTime(DateTime date, {String locale = 'en'}) {
    if (locale == 'ar') {
      return DateFormat('HH:mm', 'ar').format(date);
    }
    return DateFormat('HH:mm').format(date);
  }

  // Format relative time (e.g., "2 hours ago")
  static String formatRelativeTime(DateTime date, {String locale = 'en'}) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 7) {
      return formatDate(date, locale: locale);
    } else if (difference.inDays > 0) {
      if (locale == 'ar') {
        return difference.inDays == 1 ? 'أمس' : 'منذ ${difference.inDays} أيام';
      }
      return difference.inDays == 1 ? 'Yesterday' : '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      if (locale == 'ar') {
        return difference.inHours == 1 ? 'منذ ساعة' : 'منذ ${difference.inHours} ساعات';
      }
      return difference.inHours == 1 ? '1 hour ago' : '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      if (locale == 'ar') {
        return difference.inMinutes == 1 ? 'منذ دقيقة' : 'منذ ${difference.inMinutes} دقائق';
      }
      return difference.inMinutes == 1 ? '1 minute ago' : '${difference.inMinutes} minutes ago';
    } else {
      return locale == 'ar' ? 'الآن' : 'Now';
    }
  }

  // Format file size
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  // Format phone number
  static String formatPhoneNumber(String phone) {
    // Remove all non-numeric characters
    final cleaned = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    // Format as Saudi phone number
    if (cleaned.length == 10 && cleaned.startsWith('05')) {
      return '${cleaned.substring(0, 3)} ${cleaned.substring(3, 6)} ${cleaned.substring(6)}';
    } else if (cleaned.length == 9 && cleaned.startsWith('5')) {
      return '05${cleaned.substring(1, 2)} ${cleaned.substring(2, 5)} ${cleaned.substring(5)}';
    }
    
    return phone; // Return original if can't format
  }

  // Truncate text
  static String truncateText(String text, int maxLength, {String suffix = '...'}) {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength - suffix.length) + suffix;
  }

  // Capitalize first letter
  static String capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  // Convert to title case
  static String toTitleCase(String text) {
    return text.split(' ').map((word) => capitalizeFirst(word)).join(' ');
  }

  // Get file extension
  static String getFileExtension(String fileName) {
    return fileName.split('.').last.toLowerCase();
  }

  // Check if image file
  static bool isImageFile(String fileName) {
    final extension = getFileExtension(fileName);
    return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].contains(extension);
  }

  // Generate unique ID
  static String generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // Calculate discount percentage
  static double calculateDiscountPercentage(double originalPrice, double discountedPrice) {
    if (originalPrice <= 0) return 0;
    return ((originalPrice - discountedPrice) / originalPrice) * 100;
  }

  // Calculate rating average
  static double calculateAverageRating(List<double> ratings) {
    if (ratings.isEmpty) return 0;
    return ratings.reduce((a, b) => a + b) / ratings.length;
  }

  // Get status color based on order status
  static String getStatusColorHex(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return '#FF9800'; // Orange
      case 'processing':
        return '#2196F3'; // Blue
      case 'shipped':
        return '#9C27B0'; // Purple
      case 'delivered':
        return '#4CAF50'; // Green
      case 'cancelled':
        return '#F44336'; // Red
      default:
        return '#757575'; // Grey
    }
  }

  // Validate image size
  static bool isValidImageSize(int bytes) {
    return bytes <= 5 * 1024 * 1024; // 5MB
  }

  // Generate random color
  static String generateRandomColorHex() {
    final colors = [
      '#F44336', '#E91E63', '#9C27B0', '#673AB7',
      '#3F51B5', '#2196F3', '#03A9F4', '#00BCD4',
      '#009688', '#4CAF50', '#8BC34A', '#CDDC39',
      '#FFEB3B', '#FFC107', '#FF9800', '#FF5722'
    ];
    colors.shuffle();
    return colors.first;
  }

  // Clean and format search query
  static String cleanSearchQuery(String query) {
    return query.trim().toLowerCase().replaceAll(RegExp(r'\s+'), ' ');
  }

  // Check if string contains Arabic text
  static bool containsArabic(String text) {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  // Get text direction based on content
  static String getTextDirection(String text) {
    return containsArabic(text) ? 'rtl' : 'ltr';
  }
}
