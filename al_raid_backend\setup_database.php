<?php
require_once 'config/database.php';

echo "<h1>AL-Raid Database Setup</h1>";

try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<p>✓ Connected to MySQL server</p>";

    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS al_raid_db CHARACTER SET utf8 COLLATE utf8_general_ci");
    echo "<p>✓ Database 'al_raid_db' created</p>";

    // Use the database
    $pdo->exec("USE al_raid_db");

    // Create users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            full_name VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            phone_number VARCHAR(50) NOT NULL,
            password VARCHAR(255) NOT NULL,
            commercial_register_photo VARCHAR(255),
            shipping_address TEXT NOT NULL,
            country VARCHAR(100) NOT NULL,
            role ENUM('merchant', 'buyer') NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p>✓ Users table created</p>";

    // Create products table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            image VARCHAR(255),
            description TEXT NOT NULL,
            country_of_origin VARCHAR(100) NOT NULL,
            price DECIMAL(10, 2) NOT NULL,
            type ENUM('industrial', 'food') NOT NULL,
            status ENUM('under_review', 'approved', 'rejected') DEFAULT 'under_review',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");
    echo "<p>✓ Products table created</p>";

    // Create orders table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT NOT NULL,
            buyer_id INT NOT NULL,
            status ENUM('under_review', 'approved', 'rejected') DEFAULT 'under_review',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            FOREIGN KEY (buyer_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");
    echo "<p>✓ Orders table created</p>";

    // Create admin table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admins (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "<p>✓ Admins table created</p>";

    // Insert default admin user (username: admin, password: admin123)
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $pdo->exec("
        INSERT IGNORE INTO admins (username, password)
        VALUES ('admin', '$adminPassword')
    ");
    echo "<p>✓ Default admin user created (username: admin, password: admin123)</p>";

    // Insert sample data

    // Sample users with passwords
    $samplePassword = password_hash('123456', PASSWORD_DEFAULT);
    $pdo->exec("
        INSERT IGNORE INTO users (id, full_name, email, phone_number, password, shipping_address, country, role) VALUES
        (1, 'Ahmed Al-Rashid', '<EMAIL>', '+966501234567', '$samplePassword', '123 King Fahd Road, Riyadh', 'Saudi Arabia', 'merchant'),
        (2, 'Fatima Al-Zahra', '<EMAIL>', '+971501234567', '$samplePassword', '456 Sheikh Zayed Road, Dubai', 'United Arab Emirates', 'buyer'),
        (3, 'Mohammed Al-Kuwari', '<EMAIL>', '+974501234567', '$samplePassword', '789 Corniche Street, Doha', 'Qatar', 'merchant'),
        (4, 'Sarah Al-Sabah', '<EMAIL>', '+965501234567', '$samplePassword', '321 Gulf Road, Kuwait City', 'Kuwait', 'buyer')
    ");
    echo "<p>✓ Sample users created</p>";

    // Sample products
    $pdo->exec("
        INSERT IGNORE INTO products (id, user_id, description, country_of_origin, price, type, status) VALUES
        (1, 1, 'High-quality steel pipes for construction projects', 'Saudi Arabia', 150.00, 'industrial', 'approved'),
        (2, 1, 'Premium dates from Al-Qassim region', 'Saudi Arabia', 25.00, 'food', 'approved'),
        (3, 3, 'Industrial machinery parts and components', 'Qatar', 500.00, 'industrial', 'approved'),
        (4, 3, 'Fresh seafood from the Arabian Gulf', 'Qatar', 35.00, 'food', 'under_review'),
        (5, 1, 'Organic olive oil from traditional farms', 'Saudi Arabia', 45.00, 'food', 'approved')
    ");
    echo "<p>✓ Sample products created</p>";

    // Sample orders
    $pdo->exec("
        INSERT IGNORE INTO orders (id, product_id, buyer_id, status) VALUES
        (1, 1, 2, 'approved'),
        (2, 2, 4, 'under_review'),
        (3, 3, 2, 'approved'),
        (4, 5, 4, 'under_review')
    ");
    echo "<p>✓ Sample orders created</p>";

    echo "<h2>✅ Database setup completed successfully!</h2>";
    echo "<h3>Access Information:</h3>";
    echo "<ul>";
    echo "<li><strong>Admin Dashboard:</strong> <a href='admin/'>admin/</a></li>";
    echo "<li><strong>Admin Username:</strong> admin</li>";
    echo "<li><strong>Admin Password:</strong> admin123</li>";
    echo "<li><strong>API Endpoint:</strong> <a href='api/'>api/</a></li>";
    echo "</ul>";

    echo "<h3>Sample Data Created:</h3>";
    echo "<ul>";
    echo "<li>4 sample users (2 merchants, 2 buyers)</li>";
    echo "<li>5 sample products (3 industrial, 2 food)</li>";
    echo "<li>4 sample orders</li>";
    echo "</ul>";

} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}
h1, h2, h3 {
    color: #1976D2;
}
p {
    background: white;
    padding: 10px;
    margin: 5px 0;
    border-radius: 5px;
    border-left: 4px solid #4CAF50;
}
ul {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
a {
    color: #1976D2;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
