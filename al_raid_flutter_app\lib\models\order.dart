import 'dart:convert';
import 'user.dart';
import 'product.dart';

// إضافة OrderItem class
class OrderItem {
  final String id;
  final Product product;
  final int quantity;
  final double price;

  OrderItem({
    required this.id,
    required this.product,
    required this.quantity,
    required this.price,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product': product.toMap(),
      'quantity': quantity,
      'price': price,
    };
  }

  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      id: map['id'] ?? '',
      product: Product.fromMap(map['product']),
      quantity: map['quantity']?.toInt() ?? 0,
      price: map['price']?.toDouble() ?? 0.0,
    );
  }
}

class Order {
  final int id;
  final int productId;
  final int buyerId;
  final String status; // 'under_review', 'approved', 'rejected'
  final DateTime createdAt;
  final DateTime? updatedAt;
  
  // Additional fields from API joins
  final String? productDescription;
  final double? price;
  final String? productImage;
  final String? merchantName;
  final String? buyerName;
  final String? buyerEmail;
  final String? buyerPhone;
  
  // إضافة الخصائص المفقودة
  final User? user;
  final List<OrderItem>? items;
  final double? total;
  final String? shippingAddress;
  final String? notes;
  Order({
    required this.id,
    required this.productId,
    required this.buyerId,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.productDescription,
    this.price,
    this.productImage,
    this.merchantName,
    this.buyerName,
    this.buyerEmail,
    this.buyerPhone,
    this.user,
    this.items,
    this.total,
    this.shippingAddress,
    this.notes,
  });

  Order copyWith({
    int? id,
    int? productId,
    int? buyerId,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? productDescription,
    double? price,
    String? productImage,
    String? merchantName,
    String? buyerName,
    String? buyerEmail,
    String? buyerPhone,
  }) {
    return Order(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      buyerId: buyerId ?? this.buyerId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      productDescription: productDescription ?? this.productDescription,
      price: price ?? this.price,
      productImage: productImage ?? this.productImage,
      merchantName: merchantName ?? this.merchantName,
      buyerName: buyerName ?? this.buyerName,
      buyerEmail: buyerEmail ?? this.buyerEmail,
      buyerPhone: buyerPhone ?? this.buyerPhone,
    );
  }

  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id']?.toInt() ?? 0,
      productId: map['product_id']?.toInt() ?? 0,
      buyerId: map['buyer_id']?.toInt() ?? 0,
      status: map['status'] ?? '',
      createdAt: DateTime.tryParse(map['created_at'] ?? '') ?? DateTime.now(),
      updatedAt: map['updated_at'] != null ? DateTime.tryParse(map['updated_at']) : null,
      productDescription: map['product_description'],
      price: map['price'] != null ? (map['price'] as num).toDouble() : null,
      productImage: map['product_image'],
      merchantName: map['merchant_name'],
      buyerName: map['buyer_name'],
      buyerEmail: map['buyer_email'],
      buyerPhone: map['buyer_phone'],
    );
  }

  factory Order.fromJson(String source) => Order.fromMap(json.decode(source));

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'buyer_id': buyerId,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'product_description': productDescription,
      'price': price,
      'product_image': productImage,
      'merchant_name': merchantName,
      'buyer_name': buyerName,
      'buyer_email': buyerEmail,
      'buyer_phone': buyerPhone,
    };
  }

  String toJson() => json.encode(toMap());

  @override
  String toString() {
    return 'Order(id: $id, productId: $productId, buyerId: $buyerId, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, productDescription: $productDescription, price: $price, productImage: $productImage, merchantName: $merchantName, buyerName: $buyerName, buyerEmail: $buyerEmail, buyerPhone: $buyerPhone)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is Order &&
      other.id == id &&
      other.productId == productId &&
      other.buyerId == buyerId &&
      other.status == status &&
      other.createdAt == createdAt &&
      other.updatedAt == updatedAt &&
      other.productDescription == productDescription &&
      other.price == price &&
      other.productImage == productImage &&
      other.merchantName == merchantName &&
      other.buyerName == buyerName &&
      other.buyerEmail == buyerEmail &&
      other.buyerPhone == buyerPhone;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      productId.hashCode ^
      buyerId.hashCode ^
      status.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode ^
      productDescription.hashCode ^
      price.hashCode ^
      productImage.hashCode ^
      merchantName.hashCode ^
      buyerName.hashCode ^
      buyerEmail.hashCode ^
      buyerPhone.hashCode;
  }

  bool get isUnderReview => status == 'under_review';
  bool get isApproved => status == 'approved';
  bool get isRejected => status == 'rejected';

  String get statusDisplayName {
    switch (status) {
      case 'under_review':
        return 'Under Review';
      case 'approved':
        return 'Approved';
      case 'rejected':
        return 'Rejected';
      default:
        return status;
    }
  }

  String get productImageUrl {
    if (productImage == null || productImage!.isEmpty) return '';
    // Adjust this URL to match your API base URL
    return 'https://alraid.ridcod.com/uploads/products/$productImage';
  }
}
