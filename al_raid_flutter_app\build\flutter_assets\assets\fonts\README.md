# Font Installation Instructions

## Required Fonts

### <PERSON><PERSON><PERSON>ont (Arabic)
Download from Google Fonts: https://fonts.google.com/specimen/Ta<PERSON>wal

Required weights:
- Tajawal-Light.ttf (300)
- <PERSON><PERSON>wal-Regular.ttf (400)
- <PERSON><PERSON>wal-Medium.ttf (500)
- <PERSON><PERSON><PERSON>-Bold.ttf (700)

### Inter Font (English)
Download from Google Fonts: https://fonts.google.com/specimen/Inter

Required weights:
- Inter-Light.ttf (300)
- Inter-Regular.ttf (400)
- Inter-Medium.ttf (500)
- Inter-Bold.ttf (700)

## Installation Steps

1. Download the font files from Google Fonts
2. Place them in the `assets/fonts/` directory
3. Ensure the file names match exactly what's specified in `pubspec.yaml`
4. Run `flutter pub get` to refresh dependencies
5. Run `flutter clean` and `flutter pub get` if fonts don't appear

## Alternative: Using google_fonts Package
Instead of local font files, you can use the google_fonts package which is already included in dependencies. This will automatically download and cache the fonts.

Example usage:
```dart
Text(
  'Hello World',
  style: GoogleFonts.tajawal(
    fontSize: 16,
    fontWeight: FontWeight.w500,
  ),
)
```

Note: Using google_fonts package requires internet connection on first load but automatically handles font loading and caching.
