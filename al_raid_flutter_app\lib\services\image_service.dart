import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class ImageService {
  static final ImagePicker _picker = ImagePicker();

  // Pick single image
  static Future<File?> pickImage({
    ImageSource source = ImageSource.gallery,
    int quality = 80,
    int maxWidth = 1200,
    int maxHeight = 1200,
  }) async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: source,
        imageQuality: quality,
      );

      if (pickedFile != null) {
        final compressedFile = await compressImage(
          File(pickedFile.path),
          quality: quality,
        );
        return compressedFile;
      }
      return null;
    } catch (e) {
      debugPrint('Error picking image: $e');
      return null;
    }
  }

  // Pick multiple images
  static Future<List<File>> pickMultipleImages({
    int quality = 80,
    int maxWidth = 1200,
    int maxHeight = 1200,
    int maxImages = 5,
  }) async {
    try {
      final List<XFile> pickedFiles = await _picker.pickMultiImage(
        imageQuality: quality,
      );

      if (pickedFiles.isNotEmpty) {
        // Limit number of images
        final limitedFiles = pickedFiles.take(maxImages).toList();
        
        // Compress all images
        final List<File> compressedFiles = [];
        for (final pickedFile in limitedFiles) {
          final compressedFile = await compressImage(
            File(pickedFile.path),
            quality: quality,
          );
          if (compressedFile != null) {
            compressedFiles.add(compressedFile);
          }
        }
        return compressedFiles;
      }
      return [];
    } catch (e) {
      debugPrint('Error picking multiple images: $e');
      return [];
    }
  }

  // Compress image
  static Future<File?> compressImage(
    File file, {
    int quality = 80,
  }) async {
    try {
      final dir = await getTemporaryDirectory();
      final targetPath = path.join(
        dir.path,
        '${DateTime.now().millisecondsSinceEpoch}_compressed.jpg',
      );

      final compressedFile = await FlutterImageCompress.compressAndGetFile(
        file.absolute.path,
        targetPath,
        quality: quality,
        minWidth: 300,
        minHeight: 300,
        format: CompressFormat.jpeg,
      );

      if (compressedFile != null) {
        return File(compressedFile.path);
      }
      return file; // Return original if compression fails
    } catch (e) {
      debugPrint('Error compressing image: $e');
      return file; // Return original if compression fails
    }
  }

  // Get image size
  static Future<Size> getImageSize(File imageFile) async {
    try {
      final Uint8List bytes = await imageFile.readAsBytes();
      final image = await decodeImageFromList(bytes);
      return Size(image.width.toDouble(), image.height.toDouble());
    } catch (e) {
      debugPrint('Error getting image size: $e');
      return const Size(0, 0);
    }
  }

  // Check if file is valid image
  static bool isValidImageFile(File file) {
    final extension = path.extension(file.path).toLowerCase();
    const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
    return validExtensions.contains(extension);
  }

  // Get file size in bytes
  static Future<int> getFileSize(File file) async {
    try {
      return await file.length();
    } catch (e) {
      debugPrint('Error getting file size: $e');
      return 0;
    }
  }

  // Validate image constraints
  static Future<String?> validateImage(
    File imageFile, {
    int maxSizeBytes = 5 * 1024 * 1024, // 5MB
    int maxWidth = 4000,
    int maxHeight = 4000,
    int minWidth = 100,
    int minHeight = 100,
  }) async {
    try {
      // Check if file exists
      if (!await imageFile.exists()) {
        return 'Image file not found';
      }

      // Check file extension
      if (!isValidImageFile(imageFile)) {
        return 'Invalid image format. Please use JPG, PNG, or WebP';
      }

      // Check file size
      final fileSize = await getFileSize(imageFile);
      if (fileSize > maxSizeBytes) {
        final maxSizeMB = (maxSizeBytes / (1024 * 1024)).toStringAsFixed(1);
        return 'Image size must be less than ${maxSizeMB}MB';
      }

      // Check image dimensions
      final imageSize = await getImageSize(imageFile);
      if (imageSize.width < minWidth || imageSize.height < minHeight) {
        return 'Image must be at least ${minWidth}x${minHeight} pixels';
      }

      if (imageSize.width > maxWidth || imageSize.height > maxHeight) {
        return 'Image must be less than ${maxWidth}x${maxHeight} pixels';
      }

      return null; // No validation errors
    } catch (e) {
      debugPrint('Error validating image: $e');
      return 'Error validating image';
    }
  }

  // Delete temporary files
  static Future<void> cleanupTempFiles() async {
    try {
      final dir = await getTemporaryDirectory();
      final files = dir.listSync();
      
      for (final file in files) {
        if (file is File && file.path.contains('compressed')) {
          await file.delete();
        }
      }
    } catch (e) {
      debugPrint('Error cleaning up temp files: $e');
    }
  }

  // Show image picker options dialog
  static Future<ImageSource?> showImageSourceDialog(BuildContext context) async {
    return showDialog<ImageSource>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Image Source'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Gallery'),
                onTap: () => Navigator.pop(context, ImageSource.gallery),
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Camera'),
                onTap: () => Navigator.pop(context, ImageSource.camera),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  // Create thumbnail
  static Future<File?> createThumbnail(
    File imageFile, {
    int width = 200,
    int height = 200,
    int quality = 70,
  }) async {
    try {
      final dir = await getTemporaryDirectory();
      final targetPath = path.join(
        dir.path,
        '${DateTime.now().millisecondsSinceEpoch}_thumb.jpg',
      );      final thumbnailFile = await FlutterImageCompress.compressAndGetFile(
        imageFile.absolute.path,
        targetPath,
        quality: quality,
        minWidth: width,
        minHeight: height,
        format: CompressFormat.jpeg,
      );

      if (thumbnailFile != null) {
        return File(thumbnailFile.path);
      }
      return null;
    } catch (e) {
      debugPrint('Error creating thumbnail: $e');
      return null;
    }
  }
}
