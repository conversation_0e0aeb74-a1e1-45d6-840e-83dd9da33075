import 'package:dio/dio.dart';

void main() async {
  print('Testing API connection from Flutter...');
  
  final dio = Dio(BaseOptions(
    baseUrl: 'https://alraid.ridcod.com/api',
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 30),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  ));

  try {
    // Test health endpoint
    print('Testing health endpoint...');
    final healthResponse = await dio.get('/health');
    print('Health Status: ${healthResponse.statusCode}');
    print('Health Data: ${healthResponse.data}');
    
    // Test products endpoint
    print('\nTesting products endpoint...');
    final productsResponse = await dio.get('/products');
    print('Products Status: ${productsResponse.statusCode}');
    print('Products Data: ${productsResponse.data}');
    
    if (productsResponse.statusCode == 200) {
      final data = productsResponse.data;
      if (data['success'] == true) {
        final products = data['products'] as List;
        print('Total products: ${products.length}');
        
        // Count by type
        final industrial = products.where((p) => p['type'] == 'industrial').length;
        final food = products.where((p) => p['type'] == 'food').length;
        
        print('Industrial: $industrial, Food: $food');
        
        // Show sample products
        for (int i = 0; i < products.length && i < 3; i++) {
          final product = products[i];
          print('Product ${i + 1}: ${product['description']} (${product['type']}) - \$${product['price']}');
        }
      }
    }
    
    // Test industrial filter
    print('\nTesting industrial filter...');
    final industrialResponse = await dio.get('/products', queryParameters: {'type': 'industrial'});
    print('Industrial Status: ${industrialResponse.statusCode}');
    if (industrialResponse.statusCode == 200) {
      final data = industrialResponse.data;
      if (data['success'] == true) {
        print('Industrial products count: ${(data['products'] as List).length}');
      }
    }
    
    // Test food filter
    print('\nTesting food filter...');
    final foodResponse = await dio.get('/products', queryParameters: {'type': 'food'});
    print('Food Status: ${foodResponse.statusCode}');
    if (foodResponse.statusCode == 200) {
      final data = foodResponse.data;
      if (data['success'] == true) {
        print('Food products count: ${(data['products'] as List).length}');
      }
    }
    
  } catch (e) {
    print('Error: $e');
    if (e is DioException) {
      print('DioException type: ${e.type}');
      print('DioException message: ${e.message}');
      print('Response data: ${e.response?.data}');
      print('Response status: ${e.response?.statusCode}');
    }
  }
}
